from rest_framework import serializers
from .models import Survey, SurveyChartData, MasAgeRange, MasUserType
from mas.serializers import MasAgeRangeSerializer, MasUserTypeSerializer


class SurveySerializer(serializers.ModelSerializer):
    masAgeRange = MasAgeRangeSerializer(read_only=True)
    masUserType = MasUserTypeSerializer(read_only=True)
    masAgeRangeId = serializers.PrimaryKeyRelatedField(
        source='masAgeRange',
        queryset=MasAgeRange.objects.all()
    )
    masUserTypeId = serializers.PrimaryKeyRelatedField(
        source='masUserType',
        queryset=MasUserType.objects.all()
    )
    class Meta:
        model = Survey
        fields = '__all__'

class SurveyChartDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = SurveyChartData
        fields = ['label', 'data']