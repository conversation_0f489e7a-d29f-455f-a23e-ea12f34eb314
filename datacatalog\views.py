from django.shortcuts import render
from rest_framework import viewsets, filters
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from .models import DataCatalog
from .serializers import DataCatalogSerializer
from utils.pagination import CustomPagination
from drf_spectacular.utils import extend_schema
from rest_framework.response import Response
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time
from django.db.models import Q

@extend_schema(
    tags=["DataCatalog"]
)
class DataCatalogViewSet(viewsets.ModelViewSet):
    queryset = DataCatalog.objects.all()
    serializer_class = DataCatalogSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [filters.OrderingFilter, DjangoFilterBackend]
    filterset_fields = ['id', 'name', 'department__id', 'dataType', 'datasetType', 'dataGovernanceCategory']

    def list(self, request, *args, **kwargs):
        name = request.query_params.get('name')
        departmentId = request.query_params.get('department__id')
        dataType = request.query_params.get('dataType')
        datasetType = request.query_params.get('datasetType')
        dataGovernanceCategory = request.query_params.get('dataGovernanceCategory')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        queryset = self.get_queryset()
        if name:
            queryset = queryset.filter(name__icontains=name)
        if departmentId:
            queryset = queryset.filter(department__id=departmentId)
        if dataType:
            queryset = queryset.filter(dataType=dataType)
        if datasetType:
            queryset = queryset.filter(datasetType=datasetType)
        if dataGovernanceCategory:
            queryset = queryset.filter(dataGovernanceCategory=dataGovernanceCategory)
        if startDate:
            queryset = queryset.filter(Q(dataUpdateDate__gte=convert_str_to_date_min_time(startDate)) | Q(dataCreateDate__gte=convert_str_to_date_min_time(startDate)))
        if endDate:
            queryset = queryset.filter(Q(dataUpdateDate__lte=convert_str_to_date_max_time(endDate)) | Q(dataCreateDate__lte=convert_str_to_date_max_time(endDate)))
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

