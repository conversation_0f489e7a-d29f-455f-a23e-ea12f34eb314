from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import User, Userlog, Permission, PermissionRole, PermissionUser, Role, UserRole
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer


class PermissionSerializer(BaseModelSerializer):
    class Meta:
        model = Permission
        fields = '__all__'


class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = '__all__'


class UserSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = User
        fields = [
            'id',
            'userType',
            'firstname',
            'lastname',
            'email',
            'phoneNumber',
            'image',
            'securityStamp',
            'emailConfirmed',
            'phoneNumberConfirmed',
            'twoFactorEnabled',
            'lockoutEndDateUtc',
            'lockoutEnabled',
            'accessFailedCount',
            'status',
            'createUserId',
            'createDate',
            'updateUserId',
            'updateDate',
        ]


class RegisterRequestSerializer(serializers.Serializer):
    email = serializers.Em<PERSON><PERSON>ield(max_length=254)
    password = serializers.CharField(write_only=True)
    firstname = serializers.CharField(max_length=150, required=False)
    lastname = serializers.CharField(max_length=150, required=False)
    phoneNumber = serializers.CharField(max_length=150, required=False)
    image = serializers.ImageField(required=False)


class TokenResponseSerializer(serializers.Serializer):
    access = serializers.CharField()
    refresh = serializers.CharField()
    user = UserSerializer()


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom token serializer that avoids querying non-existent database fields
    """
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        # Add custom claims
        token['user_type'] = user.userType
        token['email'] = user.email
        token['is_admin'] = user.is_admin

        return token


class UserlogSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    userId = serializers.PrimaryKeyRelatedField(
        source='user',
        queryset=User.objects.all(),
    )

    class Meta:
        model = Userlog
        fields = '__all__'


class PermissionRoleSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)
    permission = PermissionSerializer(read_only=True)
    permissionId = serializers.PrimaryKeyRelatedField(
        source='permission',
        queryset=Permission.objects.all()
    )
    roleIds = serializers.ListField(child=serializers.IntegerField(), write_only=True)
    
    class Meta:
        model = PermissionRole
        fields = '__all__'
        
    def create(self, validated_data):
        role_ids = validated_data.pop('roleIds', [])
        permission = validated_data.get('permission')
        
        # Create multiple PermissionRole instances
        permission_roles = []
        for role_id in role_ids:
            try:
                role = Role.objects.get(id=role_id)
                # Check if the permission-role combination already exists
                permission_role, created = PermissionRole.objects.get_or_create(
                    permission=permission,
                    role=role
                )
                permission_roles.append(permission_role)
            except Role.DoesNotExist:
                continue
                
        # Return the first created instance for DRF's create method
        return permission_roles[0] if permission_roles else None
    
    def update(self, instance, validated_data):
        role_ids = validated_data.pop('roleIds', [])
        permission = validated_data.get('permission')
        
        # Get existing role IDs for this permission
        existing_role_ids = set(PermissionRole.objects.filter(permission=permission).values_list('role_id', flat=True))
        
        # Convert new role_ids to set for efficient comparison
        new_role_ids = set(role_ids)
        
        # Find roles to delete (in existing but not in new)
        roles_to_delete = existing_role_ids - new_role_ids
        
        # Find roles to add (in new but not in existing)
        roles_to_add = new_role_ids - existing_role_ids
        
        # Delete roles that are no longer needed
        if roles_to_delete:
            PermissionRole.objects.filter(
                permission=permission,
                role_id__in=roles_to_delete
            ).delete()
        
        # Add new roles
        for role_id in roles_to_add:
            try:
                role = Role.objects.get(id=role_id)
                PermissionRole.objects.create(permission=permission, role=role)
            except Role.DoesNotExist:
                continue
                
        return instance



class PermissionUserSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    permission = PermissionSerializer(read_only=True)
    userId = serializers.PrimaryKeyRelatedField(
        source='user',
        queryset=User.objects.all(),
    )
    permissionId = serializers.PrimaryKeyRelatedField(
        source='permission',
        queryset=Permission.objects.all()
    )
    class Meta:
        model = PermissionUser
        fields = '__all__'


class UserRoleSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    role = RoleSerializer(read_only=True)

    class Meta:
        model = UserRole
        fields = '__all__'
