from drf_spectacular.utils import extend_schema
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import serializers
from course.models import Course


class PowerBICourseSerializer(serializers.ModelSerializer):
    """Serializer for Power BI course data with specific field mapping"""
    university = serializers.CharField(source='universityName')
    faculty = serializers.CharField(source='facultyName')
    department = serializers.CharField(source='department')
    courseNameTh = serializers.CharField(source='programTh')
    courseNameEn = serializers.CharField(source='programEn')
    courseType = serializers.CharField(source='masCourseType.name')
    courseDetails = serializers.CharField(source='detail')
    studentsPerYear = serializers.CharField(source='numberOfStudent')
    courseWebsite = serializers.CharField(source='link')
    coordinator = serializers.Char<PERSON><PERSON>(source='contactName')
    coordinatorEmail = serializers.Char<PERSON><PERSON>(source='contactEmail')

    class Meta:
        model = Course
        fields = [
            'university', 'faculty', 'department', 'courseNameTh',
            'courseNameEn', 'courseType', 'courseDetails', 'studentsPerYear',
            'courseWebsite', 'coordinator', 'coordinatorEmail'
        ]


@extend_schema(
    tags=["Power BI"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_course(request):
    try:
        courses = Course.objects.select_related('masCourseType').order_by('-id')
        total = courses.count()

        return Response({
            "data": PowerBICourseSerializer(courses, many=True).data,
            "total": total,
        }, status=200)
    except Exception as e:
        return Response({
            "error": "Error",
            "message": str(e)
        }, status=500)
