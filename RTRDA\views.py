from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.response import Response
from rest_framework import status
from django.core.cache import cache
from django.conf import settings
import uuid

class CustomTokenRefreshView(TokenRefreshView):
    """
    Custom token refresh view that ensures tokens are properly invalidated after refresh.
    This view extends the default TokenRefreshView to track used refresh tokens.
    """
    
    def post(self, request, *args, **kwargs):
        # Get the refresh token from the request
        refresh_token = request.data.get('refresh')
        
        if not refresh_token:
            return Response(
                {"detail": "Refresh token is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            # Check if the token has been used before
            token_key = f"used_token:{refresh_token}"
            if cache.get(token_key):
                return Response(
                    {"detail": "Refresh token has already been used."},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Call the parent post method to get a new access token
            response = super().post(request, *args, **kwargs)
            
            # Mark the token as used in cache
            # Set expiry to match the refresh token lifetime
            cache.set(token_key, True, timeout=getattr(settings, 'CACHE_TTL', 86400))
            
            return response
            
        except Exception as e:
            return Response(
                {"detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            ) 