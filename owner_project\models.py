from django.db import models
from RTRDA.model import BaseModel
from mas.models import MasTrainType, MasRailwayLine

class OwnerProject(BaseModel):
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    masTrainType = models.ForeignKey(
        MasTrainType,
        on_delete=models.CASCADE,
        db_column='MasTrainTypeId'
    )
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI')
    logo = models.ImageField(db_column='Logo', upload_to='owner_project/logo/')
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'OwnerProject'


class OwnerProjectMasRailwayLine(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    ownerProject = models.ForeignKey(
        OwnerProject,
        on_delete=models.CASCADE,
        db_column='OwnerProjectId'
    )
    masRailwayLine = models.ForeignKey(
        MasRailwayLine,
        on_delete=models.CASCADE,
        db_column='MasRailwayLineId'
    )

    class Meta:
        managed = False
        db_table = 'OwnerProjectMasRailwayLine'
