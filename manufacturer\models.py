from django.db import models
from RTRDA.model import BaseModel
from mas.models import MasGeography, MasProvince, MasDistrict, MasSubdistrict, MasStandardCategory

class Manufacturer(BaseModel):
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    address = models.CharField(db_column='Address', max_length=500, db_collation='Thai_CI_AI')
    masGeography = models.ForeignKey(MasGeography, on_delete=models.PROTECT, db_column='MasGeographyId')
    masProvince = models.ForeignKey(MasProvince, on_delete=models.PROTECT, db_column='MasProvinceId')
    masDistrict = models.ForeignKey(MasDistrict, on_delete=models.PROTECT, db_column='MasDistrictId')
    masSubdistrict = models.ForeignKey(MasSubdistrict, on_delete=models.PROTECT, db_column='MasSubdistrictId')
    zipcode = models.CharField(db_column='Zipcode', max_length=5, db_collation='Thai_CI_AI')
    phoneNumber = models.CharField(db_column='PhoneNumber', max_length=50, db_collation='Thai_CI_AI')
    email = models.CharField(db_column='Email', max_length=100, db_collation='Thai_CI_AI')
    latitude = models.FloatField(db_column='Latitude')
    longitude = models.FloatField(db_column='Longitude')
    status = models.BooleanField(db_column='Status')
    views = models.IntegerField(db_column='Views', default=0)

    class Meta:
        managed = False
        db_table = 'Manufacturer'
        
        
class ManufacturerMasStandardCategory(models.Model):
    id = models.AutoField(primary_key=True)
    manufacturer = models.ForeignKey(Manufacturer, on_delete=models.CASCADE, db_column='ManufacturerId')
    masStandardCategory = models.ForeignKey(MasStandardCategory, on_delete=models.CASCADE, db_column='MasStandardCategoryId')

    class Meta:
        managed = False
        db_table = 'ManufacturerMasStandardCategory'
