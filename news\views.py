import os
from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.pagination import PageNumberPagination
from .models import NewsCategory, News, Announcement
from .serializers import NewsCategorySerializer, NewsSerializer, AnnouncementSerializer
from rest_framework.permissions import IsAuthenticatedOrReadOnly, AllowAny
from utils.pagination import CustomPagination
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser, FormParser
from django.utils import timezone
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from utils.util import convert_str_to_bool, convert_str_to_date_min_time, convert_str_to_date_max_time
from django.db.models import Q

@extend_schema(
    tags=["News"]
)
class NewsCategoryViewSet(viewsets.ModelViewSet):
    queryset = NewsCategory.objects.all()
    serializer_class = NewsCategorySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]


@extend_schema(
    tags=["News"]
)
class NewsViewSet(viewsets.ModelViewSet):
    queryset = News.objects.all()
    serializer_class = NewsSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (MultiPartParser, FormParser)
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    filterset_fields = ['name', 'newsCategory__id', 'status']
    search_fields = ['name', 'newsCategory__id']
    
    def list(self, request, *args, **kwargs):
        """
        <h1>ข้อมูลข่าวสาร</h1>
        <p>Parameter</p>
        <ul>
            <li>name: (Optional)</li>
            <li>newsCategory__id: (Optional)</li>
            <li>status: (Optional)</li>
            <li>startDate: (Optional)</li>
            <li>endDate: (Optional)</li>
        </ul>
        """
        nameFilter = request.query_params.get('name')
        newsCategory__id = request.query_params.get('newsCategory__id')
        status = request.query_params.get('status')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        userId = request.query_params.get('userId')
        queryset = self.get_queryset()
        if nameFilter:
            queryset = queryset.filter(name__icontains=nameFilter)
        if newsCategory__id:
            queryset = queryset.filter(newsCategory__id=newsCategory__id)
        if status is not None:
            status = convert_str_to_bool(status)
            queryset = queryset.filter(status=status)
        if startDate:
            queryset = queryset.filter(createDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(createDate__lte=convert_str_to_date_max_time(endDate))
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema(
    tags=["News"]
)
class AnnouncementViewSet(viewsets.ModelViewSet):
    queryset = Announcement.objects.all()
    serializer_class = AnnouncementSerializer
    pagination_class = CustomPagination
    permission_classes = [AllowAny]
    parser_classes = (MultiPartParser, FormParser)
    filter_backends = [filters.OrderingFilter, DjangoFilterBackend]
    filterset_fields = ['status']
    ordering_fields = ['createDate']
    ordering = ['-createDate']

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    # def update(self, request, *args, **kwargs):
    #     serializer = self.get_serializer(data=request.data)
    #     if serializer.is_valid():
    #         self.perform_update(serializer)
    #         return Response(serializer.data, status=status.HTTP_200_OK)
    #     return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    
