from rest_framework import serializers
from .models import NewsCategory, News, Announcement
from RTRDA.serializers import BaseModelSerializer, UserInfoSerializer


class NewsCategorySerializer(BaseModelSerializer):
    class Meta:
        model = NewsCategory
        fields = '__all__'


class NewsSerializer(BaseModelSerializer):
    newsCategory = NewsCategorySerializer(read_only=True)
    newsCategoryId = serializers.PrimaryKeyRelatedField(
        source='newsCategory', 
        queryset=NewsCategory.objects.all()
    )
    
    class Meta:
        model = News
        fields = '__all__'


class AnnouncementSerializer(BaseModelSerializer):
    class Meta:
        model = Announcement
        fields = '__all__'
        
    def create(self, validated_data):
        return Announcement.objects.create(**validated_data)

    def update(self, instance, validated_data):
        instance.file = validated_data.get('file', instance.file)
        instance.link = validated_data.get('link', instance.link)
        instance.startDate = validated_data.get('startDate', instance.startDate)
        instance.endDate = validated_data.get('endDate', instance.endDate)
        instance.status = validated_data.get('status', instance.status)
        instance.updateUserId = validated_data.get('updateUserId', instance.updateUserId)
        instance.updateDate = validated_data.get('updateDate', instance.updateDate)
        instance.save()
        return instance