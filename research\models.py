from django.db import models
from RTRDA.model import BaseModel
from rails.models import AgencySupportRailwayResearch


class Research(BaseModel):
    projectCode = models.CharField(db_column='ProjectCode', max_length=100, db_collation='Thai_CI_AI')
    projectName = models.CharField(db_column='ProjectName', max_length=500, db_collation='Thai_CI_AI')
    year = models.CharField(db_column='Year', max_length=4, db_collation='Thai_CI_AI')
    link = models.CharField(db_column='Link', max_length=200, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status')
    views = models.IntegerField(db_column='Views', default=0)
    abstract = models.TextField(db_column='Abstract', db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Research'


class ResearchAgency(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'ResearchAgency'


class ResearchResearchAgency(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    research = models.ForeignKey(Research, on_delete=models.CASCADE, db_column='ResearchId')
    researchAgency = models.ForeignKey(ResearchAgency, on_delete=models.CASCADE, db_column='ResearchAgencyId')

    class Meta:
        managed = False
        db_table = 'ResearchResearchAgency'
        unique_together = (('research', 'researchAgency'),)


class Researcher(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'Researcher'


class ResearchResearcher(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    research = models.ForeignKey(Research, on_delete=models.CASCADE, db_column='ResearchId')
    researcher = models.ForeignKey(Researcher, on_delete=models.CASCADE, db_column='ResearcherId')

    class Meta:
        managed = False
        db_table = 'ResearchResearcher'
        unique_together = (('research', 'researcher'),)


class ResearchAgencySupportRailwayResearch(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    research = models.ForeignKey(Research, on_delete=models.CASCADE, db_column='ResearchId')
    agencySupportRailwayResearch = models.ForeignKey(AgencySupportRailwayResearch, on_delete=models.CASCADE, db_column='AgencySupportRailwayResearchId')

    class Meta:
        managed = False
        db_table = 'ResearchAgencySupportRailwayResearch'
