from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import DataCatalog
from departments.models import Department
from departments.serializers import DepartmentSerializer


class DataCatalogSerializer(BaseModelSerializer):
    department = DepartmentSerializer(read_only=True)
    departmentId = serializers.PrimaryKeyRelatedField(
        source='department',
        queryset=Department.objects.all()
    )

    class Meta:
        model = DataCatalog
        fields = '__all__'
