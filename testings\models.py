from django.db import models
from RTRDA.model import BaseModel
from mas.models import Standard, MasGeography, MasProvince, MasDistrict, MasSubdistrict


class Runningnumber(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=2, db_collation='Thai_CI_AI')
    year = models.IntegerField(db_column='Year', blank=True, null=True)
    month = models.IntegerField(db_column='Month', blank=True, null=True)
    number = models.IntegerField(db_column='Number')

    class Meta:
        managed = False
        db_table = 'RunningNumber'


class Testing(BaseModel):
    standard = models.ForeignKey(Standard, on_delete=models.PROTECT, db_column='StandardId')
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    subName = models.CharField(db_column='SubName', max_length=1000, db_collation='Thai_CI_AI', blank=True, null=True)
    equipment = models.CharField(db_column='Equipment', max_length=1000, db_collation='Thai_CI_AI')
    equipmentRequirement = models.CharField(db_column='EquipmentRequirement', max_length=1000, db_collation='Thai_CI_AI')
    referenceStandard = models.TextField(db_column='ReferenceStandard', db_collation='Thai_CI_AI')
    isoLab = models.CharField(db_column='ISOLab', max_length=500, db_collation='Thai_CI_AI')
    tisiCerLab = models.CharField(db_column='TISICerLab', max_length=500, db_collation='Thai_CI_AI')
    remark = models.CharField(db_column='Remark', max_length=1000, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'Testing'
        

class TestingCenterAgency(BaseModel):
    logo = models.ImageField(db_column='Logo', upload_to='testing-center-agency/logo/', blank=True, null=True)
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    address = models.CharField(db_column='Address', max_length=500, db_collation='Thai_CI_AI')
    masGeography = models.ForeignKey(MasGeography, on_delete=models.PROTECT, db_column='MasGeographyId')
    masProvince = models.ForeignKey(MasProvince, on_delete=models.PROTECT, db_column='MasProvinceId')
    masDistrict = models.ForeignKey(MasDistrict, on_delete=models.PROTECT, db_column='MasDistrictId')
    masSubdistrict = models.ForeignKey(MasSubdistrict, on_delete=models.PROTECT, db_column='MasSubdistrictId')
    zipcode = models.CharField(db_column='Zipcode', max_length=5, db_collation='Thai_CI_AI')
    phoneNumber = models.CharField(db_column='PhoneNumber', max_length=50, db_collation='Thai_CI_AI')
    email = models.CharField(db_column='Email', max_length=100, db_collation='Thai_CI_AI')
    latitude = models.FloatField(db_column='Latitude')
    longitude = models.FloatField(db_column='Longitude')
    link = models.CharField(db_column='Link', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)
    cooperationNetworks = models.CharField(db_column='CooperationNetworks', max_length=300, db_collation='Thai_CI_AI', blank=True, null=True)
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'TestingCenterAgency'


class TestingCenter(BaseModel):
    testingCenterAgency = models.ForeignKey(TestingCenterAgency, on_delete=models.PROTECT, db_column='TestingCenterAgencyId')
    code = models.CharField(db_column='Code', max_length=10, db_collation='Thai_CI_AI',blank=True, null=True)
    banner = models.ImageField(db_column='Banner', upload_to='testing-center/banner/', blank=True, null=True)
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status')
    views = models.IntegerField(db_column='Views', default=0)

    class Meta:
        managed = False
        db_table = 'TestingCenter'
        

class Service(BaseModel):
    testingCenter = models.ForeignKey(TestingCenter, on_delete=models.PROTECT, db_column='TestingCenterId')  
    detail = models.CharField(db_column='Detail', max_length=500, db_collation='Thai_CI_AI')  
    testingBranch = models.CharField(db_column='TestingBranch', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  
    referenceStandard = models.CharField(db_column='ReferenceStandard', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  
    laboratoryAccreditation = models.CharField(db_column='LaboratoryAccreditation', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  

    class Meta:
        managed = False
        db_table = 'Service'
        

class StandardTestingCenter(models.Model):
    standard = models.ForeignKey(Standard, on_delete=models.PROTECT, db_column='StandardId') 
    testingCenter = models.ForeignKey(TestingCenter, on_delete=models.PROTECT, db_column='TestingCenterId')

    class Meta:
        managed = False
        db_table = 'StandardTestingCenter'
        unique_together = (('standard', 'testingCenter'),)