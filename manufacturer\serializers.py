from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import Manufacturer, ManufacturerMasStandardCategory
from mas.serializers import MasGeographySerializer, MasProvinceSerializer, MasDistrictSerializer, MasSubdistrictSerializer, MasStandardCategorySerializer
from mas.models import MasGeography, MasProvince, MasDistrict, MasSubdistrict, MasStandardCategory

class ManufacturerSerializer(BaseModelSerializer):
    masGeography = MasGeographySerializer(read_only=True)
    masProvince = MasProvinceSerializer(read_only=True)
    masDistrict = MasDistrictSerializer(read_only=True)
    masSubdistrict = MasSubdistrictSerializer(read_only=True)
    masGeographyId = serializers.PrimaryKeyRelatedField(
        source='masGeography', 
        queryset=MasGeography.objects.all()
    )
    masProvinceId = serializers.PrimaryKeyRelatedField(
        source='masProvince', 
        queryset=MasProvince.objects.all()
    )
    masDistrictId = serializers.PrimaryKeyRelatedField(
        source='masDistrict', 
        queryset=MasDistrict.objects.all()
    )
    masSubdistrictId = serializers.PrimaryKeyRelatedField(
        source='masSubdistrict', 
        queryset=MasSubdistrict.objects.all()
    )
    class Meta:
        model = Manufacturer
        fields = '__all__'

class ManufacturerMasStandardCategorySerializer(serializers.ModelSerializer):
    manufacturer = ManufacturerSerializer(read_only=True)
    masStandardCategory = MasStandardCategorySerializer(read_only=True)
    manufacturerId = serializers.PrimaryKeyRelatedField(
        source='manufacturer', 
        queryset=Manufacturer.objects.all()
    )
    masStandardCategoryId = serializers.PrimaryKeyRelatedField(
        source='masStandardCategory', 
        queryset=MasStandardCategory.objects.all()
    )
    
    class Meta:
        model = ManufacturerMasStandardCategory
        fields = '__all__' 