from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from utils.pagination import CustomPagination
from .models import Testing, TestingCenterAgency, TestingCenter, Service, StandardTestingCenter, Runningnumber
from components.models import TestingCenterComponent
from .serializers import TestingSerializer, TestingCenterAgencySerializer, TestingCenterSerializer, ServiceSerializer, StandardTestingCenterSerializer
from components.serializers import TestingCenterComponentSerializer
from drf_spectacular.utils import extend_schema
from rest_framework.response import Response
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.permissions import AllowAny
from django.http import HttpResponse
import pandas as pd
import io
from datetime import datetime
import urllib.parse
from mas.models import Standard, MasStandardAgency, MasProvince, MasDistrict, MasSubdistrict
from utils.util import convert_str_to_bool
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
from django.db import transaction
from rest_framework import status
from users.models import User

@extend_schema(
    tags=["Testing"]
)
class TestingViewSet(viewsets.ModelViewSet):
    queryset = Testing.objects.all()
    serializer_class = TestingSerializer
    pagination_class = CustomPagination
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['standard__id', 'name',
                     'subName', 'equipment', 'equipmentRequirement', 'status']
    filterset_fields = ['standard__id', 'name',
                        'subName', 'equipment', 'equipmentRequirement', 'status']

    def list(self, request, *args, **kwargs):
        """
        <h1>List Testing data</h1>
        <h2>Parameters:(Querystring)</h2>
        <ul>
            <li>standard__id: int</li>
            <li>name: str</li>
            <li>subName: str</li>
            <li>equipment: str</li>
            <li>equipmentRequirement: str</li>
        </ul>
        """
        standard__id = request.query_params.get('standard__id')
        name = request.query_params.get('name')
        subName = request.query_params.get('subName')
        equipment = request.query_params.get('equipment')
        equipmentRequirement = request.query_params.get('equipmentRequirement')
        status = request.query_params.get('status')
            
        queryset = self.get_queryset()
        if standard__id:
            queryset = queryset.filter(standard__id=standard__id)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if subName:
            queryset = queryset.filter(subName__icontains=subName)
        if equipment:
            queryset = queryset.filter(equipment__icontains=equipment)
        if equipmentRequirement:
            queryset = queryset.filter(
                equipmentRequirement__icontains=equipmentRequirement)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        page = self.paginate_queryset(queryset)
        if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=['post'], url_path='download-excel', permission_classes=[AllowAny])
    def download_excel(self, request, *args, **kwargs):
        """
        Download manufacturer data as Excel file
        Request Body:
        {
            "ids": [1, 2, 3, ...]  # List of manufacturer IDs to include in the export
        }
        """

        ids = request.data.get('ids')
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)

        for item in queryset:
            try:
                standard = Standard.objects.get(id=item.standard.id)
                item.standardCode = standard.code
                masStandardAgency = MasStandardAgency.objects.get(
                    id=standard.masStandardAgency.id)
                item.masStandardAgencyName = masStandardAgency.name
            except Standard.DoesNotExist:
                item.standard = None
            except MasStandardAgency.DoesNotExist:
                item.masStandardAgency = None

        # Convert data to DataFrame
        excel_data = []
        for item in queryset:
            excel_data.append({
                'หน่วยงานที่จัดทำมาตรฐาน': item.standardCode,
                'รหัสมาตรฐาน': item.masStandardAgencyName,
                'ชื่อการทดสอบ': item.name,
                'ชื่อการทดสอบย่อย': item.subName,
                'เครื่องมือและอุปกรณ์ที่ใช้ในการวัด/ทดสอบ': item.equipment,
                'ข้อกำหนดของเครื่องมือและอุปกรณ์วัด/ทดสอบ': item.equipmentRequirement,
                'มาตรฐานอ้างอิง (ต่างประเทศ)': item.referenceStandard,
                'การรับรองห้องปฏิบัติการ ISO/IEC 17025': item.isoLab,
                'การรับรองห้องปฏิบัติการ จาก สมอ.': item.tisiCerLab,
                'หมายเหตุ': item.remark,
            })

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลรายการทดสอบ (มาตรฐาน)'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)

            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']

            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })

            # Add the Thai header text
            worksheet.merge_range('A1:J1', sheet_name, header_format)

            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }

            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"

            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:J2', thai_date, date_format)

            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(
                    str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)

        excel_file.seek(0)

        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # Encode the Thai filename for better cross-browser compatibility
        # Changed '/' to '_' to avoid path issues
        filename = f'{sheet_name}.xlsx'

        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"

        response['Content-Disposition'] = content_disposition

        return response


@extend_schema(
    tags=["Testing"]
)
class TestingCenterAgencyViewSet(viewsets.ModelViewSet):
    queryset = TestingCenterAgency.objects.all()
    serializer_class = TestingCenterAgencySerializer
    pagination_class = CustomPagination
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = [
        'name',
        'masProvince__id',
        'masDistrict__id',
        'masSubdistrict__id',
        'zipcode',
        'address',
        'email',
        'phoneNumber',
        'latitude',
        'longitude',
        'status',
    ]
    search_fields = [
        'name',
        'masProvince__id',
        'masDistrict__id',
        'masSubdistrict__id',
        'zipcode',
        'address',
        'email',
        'phoneNumber',
        'latitude',
        'longitude',
        'status',
    ]

    def list(self, request, *args, **kwargs):
        """
        <h1>List Testing Center Agency data</h1>
        """
        name = request.query_params.get('name')
        provinceId = request.query_params.get('masProvince__id')
        districtId = request.query_params.get('masDistrict__id')
        subDistrictId = request.query_params.get('masSubdistrict__id')
        zipcode = request.query_params.get('zipcode')
        address = request.query_params.get('address')
        email = request.query_params.get('email')
        phoneNumber = request.query_params.get('phoneNumber')
        latitude = request.query_params.get('latitude')
        longitude = request.query_params.get('longitude')
        status = request.query_params.get('status')

        if name or provinceId or districtId or subDistrictId or zipcode or address or email or phoneNumber or latitude or longitude or status:
            queryset = self.get_queryset()
            if name:
                queryset = queryset.filter(name__icontains=name)
            if provinceId:
                queryset = queryset.filter(masProvince=provinceId)
            if districtId:
                queryset = queryset.filter(masDistrict=districtId)
            if subDistrictId:
                queryset = queryset.filter(masSubdistrict=subDistrictId)
            if zipcode:
                queryset = queryset.filter(zipcode=zipcode)
            if address:
                queryset = queryset.filter(address__icontains=address)
            if email:
                queryset = queryset.filter(email__icontains=email)
            if phoneNumber:
                queryset = queryset.filter(phoneNumber__icontains=phoneNumber)
            if latitude:
                queryset = queryset.filter(latitude__icontains=latitude)
            if longitude:
                queryset = queryset.filter(longitude__icontains=longitude)
            if status:
                queryset = queryset.filter(status=convert_str_to_bool(status))

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลหน่วยงานที่ให้บริการศูนย์การทดสอบ/ห้องปฏิบัติการระบบราง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Testing Center Agency IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')        
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
            
        for item in queryset:
            try:
                masProvince = MasProvince.objects.get(id=item.masProvince_id)
                item.masProvinceName = masProvince.name
                masDistrict = MasDistrict.objects.get(id=item.masDistrict_id)
                item.masDistrictName = masDistrict.name
                masSubdistrict = MasSubdistrict.objects.get(id=item.masSubdistrict_id)
                item.masSubdistrictName = masSubdistrict.name
            except MasProvince.DoesNotExist:
                item.masProvinceName = None
            except MasDistrict.DoesNotExist:
                item.masDistrictName = None
            except MasSubdistrict.DoesNotExist:
                item.masSubdistrictName = None
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'ชื่อหน่วยงาน': item.name,
                'ที่ตั้ง': item.address,
                'แขวง/ตำบล': item.masSubdistrictName,
                'เขต/อำเภอ': item.masDistrictName,
                'จังหวัด': item.masProvinceName,
                'รหัสไปรษณีย์': item.zipcode,
                'เบอร์ติดต่อ': item.phoneNumber,
                'อีเมล': item.email,
                'ละติจูด,ลองจิจูด': f"{item.latitude}, {item.longitude}",
                'ลิงก์เว็บไซต์ (หรือ Facebook Page)': item.link,
                'เครือข่ายความร่วมมือในประเทศ และ ต่างประเทศ (หรือ หน่วยงานที่ให้บริการทดสอบ วิจัย)': item.cooperationNetworks,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลหน่วยงานที่ให้บริการศูนย์การทดสอบ/ห้องปฏิบัติการระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:N1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:N2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["Testing"]
)
class TestingCenterViewSet(viewsets.ModelViewSet):
    queryset = TestingCenter.objects.all()
    serializer_class = TestingCenterSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = [
        'name',
        'testingCenterAgency__id',
        'status',
    ]
    ordering_fields = ['id','name', 'testingCenterAgency__name', 'status', 'code']

    def list(self, request, *args, **kwargs):
        """
        <h1>List Testing Center data</h1>
        """
        name = request.query_params.get('name')
        testingCenterAgencyId = request.query_params.get('testingCenterAgency__id')
        status = request.query_params.get('status')
        component__id = request.query_params.get('component__id')
        standard__id = request.query_params.get('standard__id')
        majorComponent = request.query_params.get('majorComponent')
        detail = request.query_params.get('detail')
        ordering = request.query_params.get('ordering')

        queryset = self.get_queryset()
        if name:
            queryset = queryset.filter(name__icontains=name)
        if testingCenterAgencyId:
            queryset = queryset.filter(
                testingCenterAgency=testingCenterAgencyId)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if component__id:
            testingCenterComponents = TestingCenterComponent.objects.filter(component__id=component__id)
            queryset = queryset.filter(id__in=testingCenterComponents.values_list('testingCenter__id', flat=True))
        if standard__id:
            standardTestingCenters = StandardTestingCenter.objects.filter(standard__id=standard__id)
            queryset = queryset.filter(id__in=standardTestingCenters.values_list('testingCenter__id', flat=True))
        if majorComponent:
            testingCenterComponents = TestingCenterComponent.objects.filter(component__majorComponents__icontains=majorComponent)
            queryset = queryset.filter(id__in=testingCenterComponents.values_list('testingCenter__id', flat=True))
        if detail:
            services = Service.objects.filter(detail__icontains=detail)
            queryset = queryset.filter(id__in=services.values_list('testingCenter__id', flat=True))
        if ordering:
            queryset = queryset.order_by(ordering)
        page = self.paginate_queryset(queryset)  
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            for item in serializer.data:
                component = TestingCenterComponent.objects.filter(testingCenter__id=item['id'])
                item['components'] = TestingCenterComponentSerializer(component, many=True).data
                services = Service.objects.filter(testingCenter__id=item['id'])
                item['services'] = ServiceSerializer(services, many=True).data
            return self.get_paginated_response(serializer.data)

        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """
        Create a new Testing Center with auto-generated code
        """
        try:
            with transaction.atomic():
                # Get the last running number
                lastRunningNumber = Runningnumber.objects.select_for_update().filter(type='LT').order_by('-number').first()
                if lastRunningNumber:
                    nextNumber = lastRunningNumber.number + 1
                else:
                    nextNumber = 1
                    
                # Add code to request data
                request.data['code'] = f"LT{nextNumber:04d}"
                
                # Create the testing center
                response = super().create(request, *args, **kwargs)
                
                # If we got here, creation was successful - update running number
                if lastRunningNumber:
                    lastRunningNumber.number = nextNumber
                    lastRunningNumber.save()
                else:
                    # Create new running number if it doesn't exist
                    current_year = datetime.now().year
                    Runningnumber.objects.create(
                        type='LT',
                        year=current_year,
                        number=nextNumber
                    )
                
                return response
        except Exception as e:
            # Log the error and re-raise it
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating TestingCenter: {str(e)}")
            raise

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลศูนย์การทดสอบ/ห้องปฏิบัติการระบบราง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Testing Center IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')        
        queryset = self.get_queryset().order_by('code')
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
            
        for item in queryset:
            try:
                testingCenterAgency = TestingCenterAgency.objects.get(id=item.testingCenterAgency_id)
                item.testingCenterAgencyName = testingCenterAgency.name
            except TestingCenterAgency.DoesNotExist:
                item.testingCenterAgencyName = None

        # Convert data to DataFrame
        excel_data = []
        count = 0
        for item in queryset:
            count += 1
            try:
                if item.updateUserId:
                    userId = item.updateUserId
                    updateDate = item.updateDate
                else:
                    userId = item.createUserId
                    updateDate = item.createDate
                user = User.objects.get(id=userId)
                updateUser = f"{user.firstname} {user.lastname}"
            except User.DoesNotExist:
                updateUser = None
            excel_data.append({
                'ลำดับ': count,
                'เลขที่': item.code,
                'ชื่อศูนย์ทดสอบ/ห้องปฏิบัติการระบบราง': item.name,
                'ชื่อหน่วยงาน': item.testingCenterAgencyName,
                'จำนวนการเข้าชม': item.views,
                'ผู้แก้ไขล่าสุด': updateUser,
                'วันที่แก้ไขล่าสุด': updateDate.strftime('%d/%m/%Y') if updateDate else None,
            })
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลศูนย์การทดสอบ/ห้องปฏิบัติการระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:G1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:G2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response

    @action(detail=False, methods=['PATCH'], url_path='update-views/(?P<pk>[^/.]+)', permission_classes=[AllowAny])
    def update_views(self, request, pk, *args, **kwargs):
        instance = self.get_queryset().get(id=pk)
        instance.views += 1
        instance.save()
        return Response({"message": "Views updated successfully."}, status=status.HTTP_200_OK)
    

@extend_schema(
    tags=["Testing"]
)
class ServiceViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['testingCenter__id', 'detail', 'testingBranch',
                     'referenceStandard', 'laboratoryAccreditation']
    filterset_fields = ['testingCenter__id', 'detail',
                        'testingBranch', 'referenceStandard', 'laboratoryAccreditation']

    def list(self, request, *args, **kwargs):
        """
        <h1>List Service data</h1>
        """
        detail = request.query_params.get('detail')
        testingBranch = request.query_params.get('testingBranch')
        referenceStandard = request.query_params.get('referenceStandard')
        laboratoryAccreditation = request.query_params.get('laboratoryAccreditation')
        testingCenterId = request.query_params.get('testingCenter__id')
        queryset = self.get_queryset()
        if detail:
            queryset = queryset.filter(detail__icontains=detail)
        if testingBranch:
            queryset = queryset.filter(testingBranch__icontains=testingBranch)
        if referenceStandard:
            queryset = queryset.filter(
                referenceStandard__icontains=referenceStandard)
        if laboratoryAccreditation:
            queryset = queryset.filter(
                laboratoryAccreditation__icontains=laboratoryAccreditation)
        if testingCenterId:
            queryset = queryset.filter(testingCenter__id=testingCenterId)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], url_path='download-excel', permission_classes=[AllowAny])
    def download_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลรายละเอียดบริการศูนย์การทดสอบ/ห้องปฏิบัติการระบบราง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of manufacturer IDs to include in the export
        }
        </pre>
        """

        ids = request.data.get('ids')
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)

        for item in queryset:
            try:
                testingCenter = TestingCenter.objects.get(
                    id=item.testingCenter.id)
                item.testingCenterName = testingCenter.name
                testingCenterAgency = TestingCenterAgency.objects.get(
                    id=testingCenter.testingCenterAgency.id)
                item.testingCenterAgencyName = testingCenterAgency.name
            except TestingCenterAgency.DoesNotExist:
                item.testingCenterAgency = None
            except TestingCenter.DoesNotExist:
                item.testingCenter = None

        # Convert data to DataFrame
        excel_data = []
        for item in queryset:
            excel_data.append({
                'หน่วยงาน': item.testingCenterName,
                'ชื่อศูนย์การทดสอบ/ห้องปฏิบัติการระบบราง': item.testingCenterAgencyName,
                'รายละเอียด (parameter)': item.detail,
                'สาขาการทดสอบ': item.testingBranch,
                'มาตรฐานอ้างอิง/วิธีทดสอบ (ต่างประเทศ)': item.referenceStandard,
                'การับรองห้องปฏิบัติการ (ISO/IEC 17025)': item.laboratoryAccreditation,
            })

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลรายละเอียดบริการ'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)

            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']

            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })

            # Add the Thai header text
            worksheet.merge_range('A1:F1', sheet_name, header_format)

            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }

            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"

            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:F2', thai_date, date_format)

            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(
                    str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)

        excel_file.seek(0)

        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # Encode the Thai filename for better cross-browser compatibility
        # Changed '/' to '_' to avoid path issues
        filename = f'{sheet_name}.xlsx'

        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"

        response['Content-Disposition'] = content_disposition

        return response

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลรายการให้บริการศูนย์การทดสอบ/ห้องปฏิบัติการระบบราง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Testing Center IDs to include in the export
            "testingCenterId": int  # Testing Center IDs to include in the export
        }
        </pre>
        """
        ids = request.data.get('ids')
        testingCenterId = request.data.get('testingCenterId')
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if testingCenterId:
            queryset = queryset.filter(testingCenter__id=testingCenterId)
        else:
            return Response({'error': 'testingCenterId is required'}, status=status.HTTP_400_BAD_REQUEST)

        for item in queryset:
            try:
                testingCenter = TestingCenter.objects.get(id=item.testingCenter.id)
                item.testingCenterName = testingCenter.name
                testingCenterAgency = TestingCenterAgency.objects.get(id=testingCenter.testingCenterAgency.id)
                item.testingCenterAgencyName = testingCenterAgency.name
            except TestingCenter.DoesNotExist:
                item.testingCenterName = None
            except TestingCenterAgency.DoesNotExist:
                item.testingCenterAgencyName = None

        # Convert data to DataFrame
        excel_data = []
        for item in queryset:
            excel_data.append({
                'รายละเอียด (parameter)': item.detail,
                'สาขาการทดสอบ': item.testingBranch,
                'มาตรฐานอ้างอิง/วิธีทดสอบ (ต่างประเทศ)': item.referenceStandard,
                'การับรองห้องปฏิบัติการ (ISO/IEC 17025)': item.laboratoryAccreditation,
            })
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลรายการให้บริการศูนย์การทดสอบ/ห้องปฏิบัติการระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=3)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:D1', sheet_name, header_format)
            
            # Add header text
            sub_header_format = workbook.add_format({
                'bold': True,
                'font_size': 12,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            sub_header_text = f'ชื่อศูนย์ทดสอบฯ/ห้องปฏิบัติฯ : {item.testingCenterName} | ชื่อหน่วยงาน : {item.testingCenterAgencyName}'
            worksheet.merge_range('A2:D2', sub_header_text, sub_header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A3:D3', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["Testing"]
)
class StandardTestingCenterViewSet(viewsets.ModelViewSet):
    queryset = StandardTestingCenter.objects.all()
    serializer_class = StandardTestingCenterSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['testingCenter__id', 'standard__id',
                     'testingCenter__testingCenterAgency__id', 'testingCenter__name']
    filterset_fields = ['testingCenter__id', 'standard__id',
                        'testingCenter__testingCenterAgency__id', 'testingCenter__name']

    def list(self, request, *args, **kwargs):
        """
        <h1>List Standard By Testing Center</h1>
        """
        testingCenterId = request.query_params.get('testingCenter__id')
        standardId = request.query_params.get('standard__id')
        testingCenterName = request.query_params.get('testingCenter__name')
        testingCenterAgencyId = request.query_params.get('testingCenter__testingCenterAgency__id')
        queryset = self.get_queryset()
        if testingCenterId:
            queryset = queryset.filter(testingCenter__id=testingCenterId)
        if standardId:
            queryset = queryset.filter(standard__id=standardId)
        if testingCenterAgencyId:
            queryset = queryset.filter(testingCenter__testingCenterAgency__id=testingCenterAgencyId)
        if testingCenterName:
            queryset = queryset.filter(testingCenter__name__icontains=testingCenterName)         
        page = self.paginate_queryset(queryset)
        if page is not None:
            for item in page:
                if item.standard.file == '-' or item.standard.file == '':
                    item.standard.file = None
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
