from django.db import models
from RTRDA.model import BaseModel

class MasAgeRange(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    ageStart = models.IntegerField()
    ageEnd = models.IntegerField()

    class Meta:
        db_table = 'MasAgeRange'

    def __str__(self):
        return self.name


class MasCourseType(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasCourseType'

    def __str__(self):
        return self.name


class MasGeography(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasGeography'

    def __str__(self):
        return self.name


class MasProvince(models.Model):
    id = models.AutoField(primary_key=True)
    masGeography = models.ForeignKey(
        MasGeography,
        on_delete=models.PROTECT,
        db_column='masGeographyId'
    )
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasProvince'

    def __str__(self):
        return self.name


class MasPosition(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasPosition'

    def __str__(self):
        return self.name


class MasDistrict(models.Model):
    id = models.AutoField(primary_key=True)
    masProvince = models.ForeignKey(
        MasProvince,
        on_delete=models.PROTECT,
        db_column='masProvinceId'
    )
    twoDigit = models.CharField(max_length=2)
    fourDigit = models.CharField(max_length=4, null=True, blank=True)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasDistrict'

    def __str__(self):
        return self.name


class MasStandardAgency(models.Model):
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=10, null=True, blank=True)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasStandardAgency'

    def __str__(self):
        return self.name


class MasRailComponentType(models.Model):
    id = models.AutoField(primary_key=True)
    masStandardAgency = models.ForeignKey(
        MasStandardAgency,
        on_delete=models.PROTECT,
        db_column='masStandardAgencyId'
    )
    code = models.CharField(max_length=10)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasRailComponentType'

    def __str__(self):
        return self.name


class MasRailwayLine(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    codeColor = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasRailwayLine'

    def __str__(self):
        return self.name


class MasSubdistrict(models.Model):
    id = models.AutoField(primary_key=True)
    masDistrict = models.ForeignKey(
        MasDistrict,
        on_delete=models.PROTECT,
        db_column='masDistrictId'
    )
    twoDigit = models.CharField(max_length=2)
    sixDigit = models.CharField(max_length=6)
    name = models.CharField(max_length=100)
    zipcode = models.CharField(max_length=6, null=True, blank=True)

    class Meta:
        db_table = 'MasSubdistrict'

    def __str__(self):
        return self.name


class MasTrainType(models.Model):
    id = models.AutoField(primary_key=True)
    masStandardAgency = models.ForeignKey(
        MasStandardAgency,
        on_delete=models.PROTECT,
        db_column='masStandardAgencyId',
        null=True,
        blank=True
    )
    code = models.CharField(max_length=10, null=True, blank=True)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasTrainType'

    def __str__(self):
        return self.name


class MasTypeOfWork(models.Model):
    id = models.AutoField(primary_key=True)
    masStandardAgency = models.ForeignKey(
        MasStandardAgency,
        on_delete=models.PROTECT,
        db_column='masStandardAgencyId'
    )
    code = models.CharField(max_length=10)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasTypeOfWork'

    def __str__(self):
        return self.name


class MasUsagePurpose(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasUsagePurpose'

    def __str__(self):
        return self.name


class MasUserType(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)

    class Meta:
        db_table = 'MasUserType'

    def __str__(self):
        return self.name


class Standard(BaseModel):
    masStandardAgency = models.ForeignKey(
        MasStandardAgency,
        on_delete=models.PROTECT,
        db_column='masStandardAgencyId'
    )
    masTrainType = models.ForeignKey(
        MasTrainType,
        on_delete=models.PROTECT,
        db_column='masTrainTypeId',
        null=True,
        blank=True
    )
    masRailComponentType = models.ForeignKey(
        MasRailComponentType,
        on_delete=models.PROTECT,
        db_column='masRailComponentTypeId',
        null=True,
        blank=True
    )
    masTypeOfWork = models.ForeignKey(
        MasTypeOfWork,
        on_delete=models.PROTECT,
        db_column='masTypeOfWorkId',
        null=True,
        blank=True
    )
    year = models.CharField(max_length=4)
    code = models.CharField(max_length=30, unique=True)
    name = models.CharField(max_length=200)
    detail = models.TextField()
    link = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(upload_to='mas/standard/doc/', null=True, blank=True)
    cover = models.ImageField(upload_to='mas/standard/cover/', null=True, blank=True)
    status = models.BooleanField(default=True)
    views = models.IntegerField(db_column='Views', default=0)

    class Meta:
        db_table = 'Standard'

    def __str__(self):
        return self.name


class MasStandardCategory(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasStandardCategory'


class StandardMasStandardCategory(models.Model):
    id = models.AutoField(primary_key=True)
    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, db_column='StandardId')
    masStandardCategory = models.ForeignKey(MasStandardCategory, on_delete=models.CASCADE, db_column='MasStandardCategoryId')

    class Meta:
        managed = False
        db_table = 'StandardMasStandardCategory'
