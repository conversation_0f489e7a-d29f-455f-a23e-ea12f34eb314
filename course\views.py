from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly, AllowAny
from rest_framework.response import Response
from .models import Course
from .serializers import CourseSerializer
from utils.pagination import CustomPagination
from rest_framework.decorators import action
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from utils.util import *
from django.http import HttpResponse
import pandas as pd
import io
import urllib.parse
from datetime import datetime
from mas.models import MasCourseType
from rest_framework import status
from users.models import User

@extend_schema(
    tags=["Course"]
)
class CourseViewSet(viewsets.ModelViewSet):
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['id', 'universityName', 'facultyName', 'programTh', 'programEn','masCourseType__id','status']
    filterset_fields = ['id', 'universityName', 'facultyName', 'programTh', 'programEn','masCourseType__id','status']
    
    def list(self, request, *args, **kwargs):
        """
        <h1>ข้อมูลหลักสูตร</h1>
        """
        id = request.query_params.get('id')
        universityName = request.query_params.get('universityName')
        facultyName = request.query_params.get('facultyName')
        programTh = request.query_params.get('programTh')
        programEn = request.query_params.get('programEn')
        masCourseType__id = request.query_params.get('masCourseType__id')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering', 'id')
        queryset = self.get_queryset()
        if id:
            queryset = queryset.filter(id=id)
        if universityName:
            queryset = queryset.filter(universityName__icontains=universityName)
        if facultyName:
            queryset = queryset.filter(facultyName__icontains=facultyName)
        if programTh:
            queryset = queryset.filter(programTh__icontains=programTh)
        if programEn:
            queryset = queryset.filter(programEn__icontains=programEn)
        if  masCourseType__id:
            queryset = queryset.filter(masCourseType__id=masCourseType__id)
        if status is not None:
            status = convert_str_to_bool(status)
            queryset = queryset.filter(status=status)
        if ordering:
            queryset = queryset.order_by(ordering)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], url_path='download-excel', permission_classes=[AllowAny])
    def download_excel(self, request, *args, **kwargs):
        """
        Download manufacturer data as Excel file
        Request Body:
        {
            "ids": [1, 2, 3, ...]  # List of manufacturer IDs to include in the export
        }
        """
        
        ids = request.data.get('ids')        
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
            
        for item in queryset:
            try:
                masCourseType = MasCourseType.objects.get(id=item.masCourseType.id)
                item.masCourseTypeName = masCourseType.name
            except MasCourseType.DoesNotExist:
                item.masCourseType = None
        

        # Convert data to DataFrame
        excel_data = []
        for item in queryset:
            excel_data.append({
                'มหาวิทยาลัย': item.universityName,
                'คณะ': item.facultyName,
                'ภาควิชา': item.department,
                'ชื่อหลักสูตร (ไทย)': item.programTh,
                'ชื่อหลักสูตร (อังกฤษ)': item.programEn,
                'ประเภทหลักสูตร': item.masCourseTypeName,
                'รายละเอียดเพิ่มเติมของหลักสูตร': item.detail,
                'จำนวนผู้เรียน (ต่อปี)': item.numberOfStudent,
                'เว็บไซต์หลักสูตร (หน่วยงาน)': item.link,
                'ผู้ประสานงาน': item.contactName,
                'Email ผู้ประสานงาน': item.contactEmail,
            })
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลหลักสูตร'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:K1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:K2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'  # Changed '/' to '_' to avoid path issues
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response

    @action(detail=False, methods=['PATCH'], url_path='update-views/(?P<pk>[^/.]+)', permission_classes=[AllowAny])
    def update_views(self, request, pk, *args, **kwargs):
        instance = self.get_queryset().get(id=pk)
        instance.views += 1
        instance.save()
        return Response({"message": "Views updated successfully."}, status=status.HTTP_200_OK)
    
    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลหลักสูตร</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Course IDs to include in the export
            "universityName": "ชื่อมหาวิทยาลัย"
            "facultyName": "ชื่อคณะ"
            "programTh": "ชื่อหลักสูตร (ไทย)"
            "programEn": "ชื่อหลักสูตร (อังกฤษ)"
            "masCourseType__id": 1
            "status": "สถานะ"
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        universityName = request.data.get('universityName')
        facultyName = request.data.get('facultyName')
        programTh = request.data.get('programTh')
        programEn = request.data.get('programEn')
        masCourseType__id = request.data.get('masCourseType__id')
        status = request.data.get('status')
        queryset = self.get_queryset()
        if universityName:
            queryset = queryset.filter(universityName__icontains=universityName)
        if facultyName:
            queryset = queryset.filter(facultyName__icontains=facultyName)
        if programTh:
            queryset = queryset.filter(programTh__icontains=programTh)
        if programEn:
            queryset = queryset.filter(programEn__icontains=programEn)
        if  masCourseType__id:
            queryset = queryset.filter(masCourseType__id=masCourseType__id)
        if status is not None:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
                
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'มหาวิทยาลัย': item.universityName,
                'คณะ': item.facultyName,
                'ภาควิชา': item.department,
                'ชื่อหลักสูตร (ไทย)': item.programTh,
                'ชื่อหลักสูตร (อังกฤษ)': item.programEn,
                'ประเภทหลักสูตร': item.masCourseType.name if item.masCourseType else None,
                'รายละเอียดเพิ่มเติมของหลักสูตร': item.detail,
                'จำนวนผู้เรียน (ต่อปี)': item.numberOfStudent,
                'เว็บไซต์หลักสูตร (หน่วยงาน)': item.link,
                'ผู้ประสานงาน': item.contactName,
                'Email ผู้ประสานงาน': item.contactEmail,
                'จำนวนเข้าชม': item.views,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลหลักสูตร'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:O1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:O2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response
