from django.db import models
from RTRDA.model import BaseModel
from departments.models import Department

class DataCatalog(BaseModel):
    department = models.ForeignKey(Department, on_delete=models.PROTECT, db_column='DepartmentId')
    name = models.CharField(db_column='Name', max_length=500, db_collation='Thai_CI_AI')
    dataType = models.CharField(db_column='DataType', max_length=1, db_collation='Thai_CI_AI')
    datasetType = models.CharField(db_column='DataSetType', max_length=50, db_collation='Thai_CI_AI')
    contactDepartmentName = models.CharField(db_column='ContactDepartmentName', max_length=200, db_collation='Thai_CI_AI')
    contactEmail = models.CharField(db_column='ContactEmail', max_length=100, db_collation='Thai_CI_AI')
    objective = models.CharField(db_column='Objective', max_length=1000, db_collation='Thai_CI_AI')
    isConsentGDCatalog = models.BooleanField(db_column='IsConsentGDCatalog')
    file = models.FileField(db_column='File',upload_to='datacatalog/', blank=True, null=True)
    sourceLink = models.CharField(db_column='SourceLink', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)
    updateFrequency = models.IntegerField(db_column='UpdateFrequency', blank=True, null=True)
    dataUpdateFrequency = models.CharField(db_column='DataUpdateFrequency', max_length=1, db_collation='Thai_CI_AI')
    geographicScope = models.CharField(db_column='GeographicScope', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)
    source = models.CharField(db_column='Source', max_length=1000, db_collation='Thai_CI_AI')
    dataStorageFormat = models.CharField(db_column='DataStorageFormat', max_length=50, db_collation='Thai_CI_AI')
    dataGovernanceCategory = models.CharField(db_column='DataGovernanceCategory', max_length=50, db_collation='Thai_CI_AI')
    dataLicenseAgreement = models.CharField(db_column='DataLicenseAgreement', max_length=50, db_collation='Thai_CI_AI')
    accessConditions = models.CharField(db_column='AccessConditions', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    url = models.CharField(db_column='Url', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    language = models.CharField(db_column='Language', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    dataCreateDate = models.DateTimeField(db_column='DataCreateDate', blank=True, null=True)
    dataUpdateDate = models.DateTimeField(db_column='DataUpdateDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'DataCatalog'

