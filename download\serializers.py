from rest_framework import serializers
from .models import DownloadRequester, DownloadRequesterDetail, DownloadChartData
from mas.serializers import MasPositionSerializer, MasUsagePurposeSerializer, MasUserTypeSerializer
from mas.models import MasPosition, MasUsagePurpose, MasUserType

class DownloadRequesterSerializer(serializers.ModelSerializer):
    masPosition = MasPositionSerializer(read_only=True)
    masUsagePurpose = MasUsagePurposeSerializer(read_only=True)
    masUserType = MasUserTypeSerializer(read_only=True)
    masPositionId = serializers.PrimaryKeyRelatedField(
        queryset=MasPosition.objects.all(),
        source='masPosition'
    )
    masUsagePurposeId = serializers.PrimaryKeyRelatedField(
        queryset=MasUsagePurpose.objects.all(),
        source='masUsagePurpose'
    )
    masUserTypeId = serializers.PrimaryKeyRelatedField(
        queryset=MasUserType.objects.all(),
        source='masUserType'
    )
    
    class Meta:
        model = DownloadRequester
        fields = '__all__'
        
class DownloadRequesterDetailSerializer(serializers.ModelSerializer):
    downloadRequester = DownloadRequesterSerializer(read_only=True)
    downloadRequesterId = serializers.PrimaryKeyRelatedField(
        queryset=DownloadRequester.objects.all(),
        source='downloadRequester'
    )
    
    class Meta:
        model = DownloadRequesterDetail
        fields = '__all__'

class DownloadChartDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadChartData
        fields = ['label', 'data']