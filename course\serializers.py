from rest_framework import serializers
from .models import Course
from mas.serializers import MasCourseTypeSerializer
from mas.models import MasCourseType
from RTRDA.serializers import BaseModelSerializer

class CourseSerializer(BaseModelSerializer):
    masCourseType = MasCourseTypeSerializer(read_only=True)
    masCourseTypeId = serializers.PrimaryKeyRelatedField(
        source='masCourseType', 
        queryset=MasCourseType.objects.all()
    )
    
    class Meta:
        model = Course
        fields = '__all__' 