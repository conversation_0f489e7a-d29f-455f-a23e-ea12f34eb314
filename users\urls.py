from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
)
from rest_framework.permissions import AllowAny
from . import views
from RTRDA.views import CustomTokenRefreshView
from .serializers import CustomTokenObtainPairSerializer

class PublicTokenObtainPairView(TokenObtainPairView):
    authentication_classes = []           # Empty list means no authentication required
    permission_classes = [AllowAny]      # Allow any access
    serializer_class = CustomTokenObtainPairSerializer

class PublicTokenRefreshView(CustomTokenRefreshView):
    authentication_classes = []           # Empty list means no authentication required
    permission_classes = [AllowAny]      # Allow any access

router = DefaultRouter()
router.register("", views.UserViewSet, basename="user")

urlpatterns = [
    path("token/", PublicTokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("token/refresh/", PublicTokenRefreshView.as_view(), name="token_refresh"),
    path("users/", include(router.urls)),
]
