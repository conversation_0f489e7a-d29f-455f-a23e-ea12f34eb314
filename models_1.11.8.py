# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class Agencysupportrailwayresearch(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    logo = models.CharField(db_column='Logo', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'AgencySupportRailwayResearch'


class Announcement(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    file = models.CharField(db_column='File', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    startdate = models.DateTimeField(db_column='StartDate', blank=True, null=True)  # Field name made lowercase.
    enddate = models.DateTimeField(db_column='EndDate', blank=True, null=True)  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Announcement'


class Component(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    componenttypeid = models.IntegerField(db_column='ComponentTypeId')  # Field name made lowercase.
    componentgroupid = models.IntegerField(db_column='ComponentGroupId')  # Field name made lowercase.
    majorsubsystem = models.CharField(db_column='MajorSubSystem', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    majorcomponents = models.CharField(db_column='MajorComponents', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    subcomponents = models.CharField(db_column='SubComponents', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    views = models.IntegerField(db_column='Views')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Component'


class Componentgroup(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ComponentGroup'


class Componenttype(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ComponentType'


class Course(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    universityname = models.CharField(db_column='UniversityName', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    facultyname = models.CharField(db_column='FacultyName', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    department = models.CharField(db_column='Department', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    programth = models.CharField(db_column='ProgramTh', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    programen = models.CharField(db_column='ProgramEn', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    mascoursetypeid = models.IntegerField(db_column='MasCourseTypeId')  # Field name made lowercase.
    detail = models.CharField(db_column='Detail', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    numberofstudent = models.CharField(db_column='NumberOfStudent', max_length=10, db_collation='Thai_CI_AI')  # Field name made lowercase.
    link = models.CharField(db_column='Link', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    contactname = models.CharField(db_column='ContactName', max_length=300, db_collation='Thai_CI_AI')  # Field name made lowercase.
    contactemail = models.CharField(db_column='ContactEmail', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    views = models.IntegerField(db_column='Views')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Course'


class Datacatalog(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    departmentid = models.IntegerField(db_column='DepartmentId')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    datatype = models.CharField(db_column='DataType', max_length=1, db_collation='Thai_CI_AI')  # Field name made lowercase.
    datasettype = models.CharField(db_column='DataSetType', max_length=50, db_collation='Thai_CI_AI')  # Field name made lowercase.
    contactdepartmentname = models.CharField(db_column='ContactDepartmentName', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    contactemail = models.CharField(db_column='ContactEmail', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    objective = models.CharField(db_column='Objective', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    isconsentgdcatalog = models.BooleanField(db_column='IsConsentGDCatalog')  # Field name made lowercase.
    file = models.CharField(db_column='File', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    sourcelink = models.CharField(db_column='SourceLink', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    updatefrequency = models.IntegerField(db_column='UpdateFrequency', blank=True, null=True)  # Field name made lowercase.
    dataupdatefrequency = models.CharField(db_column='DataUpdateFrequency', max_length=1, db_collation='Thai_CI_AI')  # Field name made lowercase.
    geographicscope = models.CharField(db_column='GeographicScope', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    source = models.CharField(db_column='Source', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    datastorageformat = models.CharField(db_column='DataStorageFormat', max_length=50, db_collation='Thai_CI_AI')  # Field name made lowercase.
    datagovernancecategory = models.CharField(db_column='DataGovernanceCategory', max_length=50, db_collation='Thai_CI_AI')  # Field name made lowercase.
    datalicenseagreement = models.CharField(db_column='DataLicenseAgreement', max_length=50, db_collation='Thai_CI_AI')  # Field name made lowercase.
    accessconditions = models.CharField(db_column='AccessConditions', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    url = models.CharField(db_column='Url', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    language = models.CharField(db_column='Language', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    datacreatedate = models.DateTimeField(db_column='DataCreateDate', blank=True, null=True)  # Field name made lowercase.
    dataupdatedate = models.DateTimeField(db_column='DataUpdateDate', blank=True, null=True)  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'DataCatalog'


class Department(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    logo = models.CharField(db_column='Logo', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Department'


class Downloadrequester(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    masusertypeid = models.IntegerField(db_column='MasUserTypeId')  # Field name made lowercase.
    departmentname = models.CharField(db_column='DepartmentName', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    coordinatorname = models.CharField(db_column='CoordinatorName', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    maspositionid = models.IntegerField(db_column='MasPositionId')  # Field name made lowercase.
    positionother = models.CharField(db_column='PositionOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    masusagepurposeid = models.IntegerField(db_column='MasUsagePurposeId')  # Field name made lowercase.
    usagepurposeother = models.CharField(db_column='UsagePurposeOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    menu = models.CharField(db_column='Menu', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    ipaddress = models.CharField(db_column='IpAddress', max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    browser = models.CharField(db_column='Browser', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    version = models.CharField(db_column='Version', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    platform = models.CharField(db_column='Platform', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'DownloadRequester'


class Downloadrequesterdetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    downloadrequesterid = models.IntegerField(db_column='DownloadRequesterId')  # Field name made lowercase.
    type = models.CharField(db_column='Type', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    refid = models.IntegerField(db_column='RefId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'DownloadRequesterDetail'


class Externaldata(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    departmentid = models.IntegerField(db_column='DepartmentId', blank=True, null=True)  # Field name made lowercase.
    type = models.CharField(db_column='Type', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    data = models.TextField(db_column='Data', db_collation='Thai_CI_AI')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ExternalData'


class Manufacturer(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    masgeographyid = models.IntegerField(db_column='MasGeographyId')  # Field name made lowercase.
    masprovinceid = models.IntegerField(db_column='MasProvinceId')  # Field name made lowercase.
    masdistrictid = models.IntegerField(db_column='MasDistrictId')  # Field name made lowercase.
    massubdistrictid = models.IntegerField(db_column='MasSubdistrictId')  # Field name made lowercase.
    zipcode = models.CharField(db_column='Zipcode', max_length=5, db_collation='Thai_CI_AI')  # Field name made lowercase.
    phonenumber = models.CharField(db_column='PhoneNumber', max_length=50, db_collation='Thai_CI_AI')  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    latitude = models.FloatField(db_column='Latitude')  # Field name made lowercase.
    longitude = models.FloatField(db_column='Longitude')  # Field name made lowercase.
    views = models.IntegerField(db_column='Views')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Manufacturer'


class Manufacturercomponent(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    type = models.CharField(db_column='Type', max_length=1, db_collation='Thai_CI_AI')  # Field name made lowercase.
    manufacturerid = models.IntegerField(db_column='ManufacturerId')  # Field name made lowercase.
    componentid = models.IntegerField(db_column='ComponentId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ManufacturerComponent'


class Masagerange(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    agestart = models.IntegerField(db_column='AgeStart')  # Field name made lowercase.
    ageend = models.IntegerField(db_column='AgeEnd')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasAgeRange'


class Mascoursetype(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasCourseType'


class Masdistrict(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    masprovinceid = models.IntegerField(db_column='MasProvinceId')  # Field name made lowercase.
    twodigit = models.CharField(db_column='TwoDigit', max_length=2, db_collation='Thai_CI_AI')  # Field name made lowercase.
    fourdigit = models.CharField(db_column='FourDigit', max_length=4, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasDistrict'


class Masgeography(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasGeography'


class Masposition(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasPosition'


class Masprovince(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    masgeographyid = models.IntegerField(db_column='MasGeographyId')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasProvince'


class Masrailcomponenttype(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    masstandardagencyid = models.IntegerField(db_column='MasStandardAgencyId')  # Field name made lowercase.
    code = models.CharField(db_column='Code', max_length=10, db_collation='Thai_CI_AI')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasRailComponentType'


class Masrailwayline(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    codecolor = models.CharField(db_column='CodeColor', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasRailwayLine'


class Masstandardagency(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    code = models.CharField(db_column='Code', max_length=10, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasStandardAgency'


class Massubdistrict(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    masdistrictid = models.IntegerField(db_column='MasDistrictId')  # Field name made lowercase.
    twodigit = models.CharField(db_column='TwoDigit', max_length=2, db_collation='Thai_CI_AI')  # Field name made lowercase.
    sixdigit = models.CharField(db_column='SixDigit', max_length=6, db_collation='Thai_CI_AI')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    zipcode = models.CharField(db_column='Zipcode', max_length=6, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasSubdistrict'


class Mastraintype(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    masstandardagencyid = models.IntegerField(db_column='MasStandardAgencyId', blank=True, null=True)  # Field name made lowercase.
    code = models.CharField(db_column='Code', max_length=10, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasTrainType'


class Mastypeofwork(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    masstandardagencyid = models.IntegerField(db_column='MasStandardAgencyId')  # Field name made lowercase.
    code = models.CharField(db_column='Code', max_length=10, db_collation='Thai_CI_AI')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasTypeOfWork'


class Masusagepurpose(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasUsagePurpose'


class Masusertype(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'MasUserType'


class News(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    newscategoryid = models.IntegerField(db_column='NewsCategoryId')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=250, db_collation='Thai_CI_AI')  # Field name made lowercase.
    detail = models.TextField(db_column='Detail', db_collation='Thai_CI_AI')  # Field name made lowercase.
    cover = models.CharField(db_column='Cover', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'News'


class Newscategory(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'NewsCategory'


class Ownerproject(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    mastraintypeid = models.IntegerField(db_column='MasTrainTypeId')  # Field name made lowercase.
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    logo = models.CharField(db_column='Logo', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'OwnerProject'


class Ownerprojectmasrailwayline(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ownerprojectid = models.IntegerField(db_column='OwnerProjectId')  # Field name made lowercase.
    masrailwaylineid = models.IntegerField(db_column='MasRailwayLineId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'OwnerProjectMasRailwayLine'


class Permission(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=250, db_collation='Thai_CI_AI')  # Field name made lowercase.
    description = models.CharField(db_column='Description', max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Permission'


class Permissionrole(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    roleid = models.IntegerField(db_column='RoleId')  # Field name made lowercase.
    permissionid = models.ForeignKey(Permission, models.DO_NOTHING, db_column='PermissionId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'PermissionRole'
        unique_together = (('permissionid', 'roleid'),)


class Permissionuser(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    userid = models.IntegerField(db_column='UserId')  # Field name made lowercase.
    permissionid = models.ForeignKey(Permission, models.DO_NOTHING, db_column='PermissionId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'PermissionUser'
        unique_together = (('permissionid', 'userid'),)


class Railserviceprovider(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    mastraintypeid = models.IntegerField(db_column='MasTrainTypeId')  # Field name made lowercase.
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    logo = models.CharField(db_column='Logo', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'RailServiceProvider'


class Railserviceprovidermasrailwayline(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    railserviceproviderid = models.IntegerField(db_column='RailServiceProviderId')  # Field name made lowercase.
    masrailwaylineid = models.IntegerField(db_column='MasRailwayLineId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'RailServiceProviderMasRailwayLine'
        unique_together = (('masrailwaylineid', 'railserviceproviderid'),)


class Railwayengineeringstandard(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    file = models.CharField(db_column='File', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    cover = models.CharField(db_column='Cover', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    views = models.IntegerField(db_column='Views')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'RailwayEngineeringStandard'


class Research(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    projectcode = models.CharField(db_column='ProjectCode', max_length=30, db_collation='Thai_CI_AI')  # Field name made lowercase.
    projectname = models.CharField(db_column='ProjectName', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    year = models.CharField(db_column='Year', max_length=4, db_collation='Thai_CI_AI')  # Field name made lowercase.
    link = models.CharField(db_column='Link', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    views = models.IntegerField(db_column='Views')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.
    abstract = models.TextField(db_column='Abstract', db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Research'


class Researchagency(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ResearchAgency'


class Researchagencysupportrailwayresearch(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    researchid = models.IntegerField(db_column='ResearchId')  # Field name made lowercase.
    agencysupportrailwayresearchid = models.IntegerField(db_column='AgencySupportRailwayResearchId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ResearchAgencySupportRailwayResearch'


class Researchresearchagency(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    researchid = models.IntegerField(db_column='ResearchId')  # Field name made lowercase.
    researchagencyid = models.IntegerField(db_column='ResearchAgencyId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ResearchResearchAgency'
        unique_together = (('researchagencyid', 'researchid'),)


class Researchresearcher(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    researchid = models.IntegerField(db_column='ResearchId')  # Field name made lowercase.
    researcherid = models.IntegerField(db_column='ResearcherId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'ResearchResearcher'
        unique_together = (('researcherid', 'researchid'),)


class Researcher(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Researcher'


class Role(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    descriptionth = models.CharField(db_column='DescriptionTh', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    descriptionen = models.CharField(db_column='DescriptionEn', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Role'


class Runningnumber(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    type = models.CharField(db_column='Type', max_length=2, db_collation='Thai_CI_AI')  # Field name made lowercase.
    year = models.IntegerField(db_column='Year', blank=True, null=True)  # Field name made lowercase.
    month = models.IntegerField(db_column='Month', blank=True, null=True)  # Field name made lowercase.
    number = models.IntegerField(db_column='Number')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'RunningNumber'


class Service(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    testingcenterid = models.IntegerField(db_column='TestingCenterId')  # Field name made lowercase.
    detail = models.CharField(db_column='Detail', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    testingbranch = models.CharField(db_column='TestingBranch', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    referencestandard = models.CharField(db_column='ReferenceStandard', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    laboratoryaccreditation = models.CharField(db_column='LaboratoryAccreditation', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Service'


class Standard(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    masstandardagencyid = models.IntegerField(db_column='MasStandardAgencyId')  # Field name made lowercase.
    mastraintypeid = models.IntegerField(db_column='MasTrainTypeId', blank=True, null=True)  # Field name made lowercase.
    masrailcomponenttypeid = models.IntegerField(db_column='MasRailComponentTypeId', blank=True, null=True)  # Field name made lowercase.
    mastypeofworkid = models.IntegerField(db_column='MasTypeOfWorkId', blank=True, null=True)  # Field name made lowercase.
    year = models.CharField(db_column='Year', max_length=4, db_collation='Thai_CI_AI')  # Field name made lowercase.
    code = models.CharField(db_column='Code', unique=True, max_length=30, db_collation='Thai_CI_AI')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    detail = models.TextField(db_column='Detail', db_collation='Thai_CI_AI')  # Field name made lowercase.
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    file = models.CharField(db_column='File', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    cover = models.CharField(db_column='Cover', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    views = models.IntegerField(db_column='Views')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Standard'


class Standardcomponent(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    standardid = models.IntegerField(db_column='StandardId')  # Field name made lowercase.
    componentid = models.IntegerField(db_column='ComponentId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'StandardComponent'


class Standardtestingcenter(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    standardid = models.IntegerField(db_column='StandardId')  # Field name made lowercase.
    testingcenterid = models.IntegerField(db_column='TestingCenterId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'StandardTestingCenter'
        unique_together = (('standardid', 'testingcenterid'),)


class Survey(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    masagerangeid = models.IntegerField(db_column='MasAgeRangeId')  # Field name made lowercase.
    masusertypeid = models.IntegerField(db_column='MasUserTypeId')  # Field name made lowercase.
    departmentname = models.CharField(db_column='DepartmentName', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    isobjectivestatistic = models.BooleanField(db_column='IsObjectiveStatistic')  # Field name made lowercase.
    isobjectiveroute = models.BooleanField(db_column='IsObjectiveRoute')  # Field name made lowercase.
    isobjectiveunderconstruction = models.BooleanField(db_column='IsObjectiveUnderConstruction')  # Field name made lowercase.
    isobjectivesafety = models.BooleanField(db_column='IsObjectiveSafety')  # Field name made lowercase.
    isobjectiveindustrial = models.BooleanField(db_column='IsObjectiveIndustrial')  # Field name made lowercase.
    isobjectiveequipment = models.BooleanField(db_column='IsObjectiveEquipment')  # Field name made lowercase.
    isobjectivestandard = models.BooleanField(db_column='IsObjectiveStandard')  # Field name made lowercase.
    isobjectivetestingcenter = models.BooleanField(db_column='IsObjectiveTestingCenter')  # Field name made lowercase.
    isobjectiverailwayindustry = models.BooleanField(db_column='IsObjectiveRailwayIndustry')  # Field name made lowercase.
    isobjectivemaintenance = models.BooleanField(db_column='IsObjectiveMaintenance')  # Field name made lowercase.
    isobjectiveenvironment = models.BooleanField(db_column='IsObjectiveEnvironment')  # Field name made lowercase.
    isobjectiveresearch = models.BooleanField(db_column='IsObjectiveResearch')  # Field name made lowercase.
    isobjectivecourse = models.BooleanField(db_column='IsObjectiveCourse')  # Field name made lowercase.
    isobjectivetraning = models.BooleanField(db_column='IsObjectiveTraning')  # Field name made lowercase.
    isobjectivenews = models.BooleanField(db_column='IsObjectiveNews')  # Field name made lowercase.
    isobjectiveother = models.BooleanField(db_column='IsObjectiveOther')  # Field name made lowercase.
    objectiveother = models.CharField(db_column='ObjectiveOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    isaccessdashboard = models.BooleanField(db_column='IsAccessDashboard')  # Field name made lowercase.
    isaccessmap = models.BooleanField(db_column='IsAccessMap')  # Field name made lowercase.
    isaccessdigitaldata = models.BooleanField(db_column='IsAccessDigitalData')  # Field name made lowercase.
    isaccessdocument = models.BooleanField(db_column='IsAccessDocument')  # Field name made lowercase.
    isaccessother = models.BooleanField(db_column='IsAccessOther')  # Field name made lowercase.
    accessother = models.CharField(db_column='AccessOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    isupdaterealtime = models.BooleanField(db_column='IsUpdateRealTime')  # Field name made lowercase.
    isupdatedaily = models.BooleanField(db_column='IsUpdateDaily')  # Field name made lowercase.
    isupdatemonthly = models.BooleanField(db_column='IsUpdateMonthly')  # Field name made lowercase.
    isupdateyearly = models.BooleanField(db_column='IsUpdateYearly')  # Field name made lowercase.
    issupportinformationtracking = models.BooleanField(db_column='IsSupportInformationTracking')  # Field name made lowercase.
    issupportbusinessanalysis = models.BooleanField(db_column='IsSupportBusinessAnalysis')  # Field name made lowercase.
    issupportpolicyformulation = models.BooleanField(db_column='IsSupportPolicyFormulation')  # Field name made lowercase.
    issupportresearch = models.BooleanField(db_column='IsSupportResearch')  # Field name made lowercase.
    issupportother = models.BooleanField(db_column='IsSupportOther')  # Field name made lowercase.
    supportother = models.CharField(db_column='SupportOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    ischannelwebsite = models.BooleanField(db_column='IsChannelWebsite')  # Field name made lowercase.
    ischannelapplication = models.BooleanField(db_column='IsChannelApplication')  # Field name made lowercase.
    ischannelapi = models.BooleanField(db_column='IsChannelAPI')  # Field name made lowercase.
    ischannelcontact = models.BooleanField(db_column='IsChannelContact')  # Field name made lowercase.
    ischannelother = models.BooleanField(db_column='IsChannelOther')  # Field name made lowercase.
    channelother = models.CharField(db_column='ChannelOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Survey'


class Testing(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    standardid = models.IntegerField(db_column='StandardId')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    subname = models.CharField(db_column='SubName', max_length=1000, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    equipment = models.CharField(db_column='Equipment', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    equipmentrequirement = models.CharField(db_column='EquipmentRequirement', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    referencestandard = models.TextField(db_column='ReferenceStandard', db_collation='Thai_CI_AI')  # Field name made lowercase.
    isolab = models.CharField(db_column='ISOLab', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    tisicerlab = models.CharField(db_column='TISICerLab', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    remark = models.CharField(db_column='Remark', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Testing'


class Testingcenter(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    testingcenteragencyid = models.IntegerField(db_column='TestingCenterAgencyId')  # Field name made lowercase.
    code = models.CharField(db_column='Code', max_length=10, db_collation='Thai_CI_AI')  # Field name made lowercase.
    banner = models.CharField(db_column='Banner', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    views = models.IntegerField(db_column='Views')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'TestingCenter'


class Testingcenteragency(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    logo = models.CharField(db_column='Logo', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    address = models.CharField(db_column='Address', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    masgeographyid = models.IntegerField(db_column='MasGeographyId')  # Field name made lowercase.
    masprovinceid = models.IntegerField(db_column='MasProvinceId')  # Field name made lowercase.
    masdistrictid = models.IntegerField(db_column='MasDistrictId')  # Field name made lowercase.
    massubdistrictid = models.IntegerField(db_column='MasSubdistrictId')  # Field name made lowercase.
    zipcode = models.CharField(db_column='Zipcode', max_length=5, db_collation='Thai_CI_AI')  # Field name made lowercase.
    phonenumber = models.CharField(db_column='PhoneNumber', max_length=50, db_collation='Thai_CI_AI')  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    latitude = models.FloatField(db_column='Latitude')  # Field name made lowercase.
    longitude = models.FloatField(db_column='Longitude')  # Field name made lowercase.
    link = models.CharField(db_column='Link', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    cooperationnetworks = models.CharField(db_column='CooperationNetworks', max_length=300, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'TestingCenterAgency'


class Testingcentercomponent(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    testingcenterid = models.IntegerField(db_column='TestingCenterId')  # Field name made lowercase.
    componentid = models.IntegerField(db_column='ComponentId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'TestingCenterComponent'


class Training(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    detail = models.TextField(db_column='Detail', db_collation='Thai_CI_AI')  # Field name made lowercase.
    startdate = models.DateTimeField(db_column='StartDate')  # Field name made lowercase.
    enddate = models.DateTimeField(db_column='EndDate')  # Field name made lowercase.
    trainingcategoryid = models.IntegerField(db_column='TrainingCategoryId')  # Field name made lowercase.
    cover = models.CharField(db_column='Cover', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    views = models.IntegerField(db_column='Views')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Training'


class Trainingcategory(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'TrainingCategory'


class Underconstruction(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    code = models.CharField(db_column='Code', max_length=20, db_collation='Thai_CI_AI')  # Field name made lowercase.
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    detail = models.CharField(db_column='Detail', max_length=1000, db_collation='Thai_CI_AI')  # Field name made lowercase.
    masrailwaylineid = models.IntegerField(db_column='MasRailwayLineId')  # Field name made lowercase.
    latitude = models.FloatField(db_column='Latitude')  # Field name made lowercase.
    longitude = models.FloatField(db_column='Longitude')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'UnderConstruction'


class User(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    usertype = models.CharField(db_column='UserType', max_length=1, db_collation='Thai_CI_AI')  # Field name made lowercase.
    firstname = models.CharField(db_column='Firstname', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    lastname = models.CharField(db_column='Lastname', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    passwordhash = models.CharField(db_column='PasswordHash', max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    email = models.CharField(db_column='Email', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    phonenumber = models.CharField(db_column='PhoneNumber', max_length=10, db_collation='Thai_CI_AI')  # Field name made lowercase.
    image = models.CharField(db_column='Image', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    securitystamp = models.CharField(db_column='SecurityStamp', max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    emailconfirmed = models.BooleanField(db_column='EmailConfirmed')  # Field name made lowercase.
    phonenumberconfirmed = models.BooleanField(db_column='PhoneNumberConfirmed')  # Field name made lowercase.
    twofactorenabled = models.BooleanField(db_column='TwoFactorEnabled')  # Field name made lowercase.
    lockoutenddateutc = models.DateTimeField(db_column='LockoutEndDateUtc', blank=True, null=True)  # Field name made lowercase.
    lockoutenabled = models.BooleanField(db_column='LockoutEnabled')  # Field name made lowercase.
    accessfailedcount = models.IntegerField(db_column='AccessFailedCount')  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId', blank=True, null=True)  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'User'


class Userclaim(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    userid = models.ForeignKey(User, models.DO_NOTHING, db_column='UserId')  # Field name made lowercase.
    claimtype = models.CharField(db_column='ClaimType', max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    claimvalue = models.CharField(db_column='ClaimValue', max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'UserClaim'


class Userlog(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    userid = models.IntegerField(db_column='UserId')  # Field name made lowercase.
    header = models.CharField(db_column='Header', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    detail = models.CharField(db_column='Detail', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    ipaddress = models.CharField(db_column='IpAddress', max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    browser = models.CharField(db_column='Browser', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    version = models.CharField(db_column='Version', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    platform = models.CharField(db_column='Platform', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'UserLog'


class Userlogin(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    loginprovider = models.CharField(db_column='LoginProvider', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    providerkey = models.CharField(db_column='ProviderKey', max_length=100, db_collation='Thai_CI_AI')  # Field name made lowercase.
    userid = models.ForeignKey(User, models.DO_NOTHING, db_column='UserId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'UserLogin'
        unique_together = (('loginprovider', 'providerkey', 'userid'),)


class Userrole(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    userid = models.ForeignKey(User, models.DO_NOTHING, db_column='UserId')  # Field name made lowercase.
    roleid = models.ForeignKey(Role, models.DO_NOTHING, db_column='RoleId')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'UserRole'
        unique_together = (('roleid', 'userid'),)


class Websitestatistics(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    page = models.CharField(db_column='Page', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    ipaddress = models.CharField(db_column='IpAddress', max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    browser = models.CharField(db_column='Browser', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    version = models.CharField(db_column='Version', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    platform = models.CharField(db_column='Platform', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'WebsiteStatistics'
