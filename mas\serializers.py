from rest_framework import serializers
from .models import (
    MasAgeRange, MasCourseType, MasDistrict, 
    MasGeography, MasProvince, MasPosition,
    MasStandardAgency, MasStandardCategory, MasRailComponentType, MasRailwayLine,
    MasSubdistrict, MasTrainType, MasTypeOfWork,
    MasUsagePurpose, MasUserType, Standard, StandardMasStandardCategory
)
from RTRDA.serializers import BaseModelSerializer

class MasAgeRangeSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasAgeRange
        fields = '__all__'

class MasCourseTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasCourseType
        fields = '__all__'

class MasGeographySerializer(serializers.ModelSerializer):
    class Meta:
        model = MasGeography
        fields = '__all__'

class MasProvinceSerializer(serializers.ModelSerializer):
    masGeography = MasGeographySerializer(read_only=True)
    masGeographyId = serializers.PrimaryKeyRelatedField(
        source='masGeography', 
        queryset=MasGeography.objects.all()
    )
    class Meta:
        model = MasProvince
        fields = '__all__'

class MasPositionSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasPosition
        fields = '__all__'

class MasDistrictSerializer(serializers.ModelSerializer):
    masProvince = MasProvinceSerializer(read_only=True)
    masProvinceId = serializers.PrimaryKeyRelatedField(
        source='masProvince', 
        queryset=MasProvince.objects.all()
    )
    class Meta:
        model = MasDistrict
        fields = '__all__'

class MasStandardAgencySerializer(serializers.ModelSerializer):
    class Meta:
        model = MasStandardAgency
        fields = '__all__'

class MasStandardCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = MasStandardCategory
        fields = '__all__'

class MasRailComponentTypeSerializer(serializers.ModelSerializer):
    masStandardAgency = MasStandardAgencySerializer(read_only=True)
    masStandardAgencyId = serializers.PrimaryKeyRelatedField(
        source='masStandardAgency', 
        queryset=MasStandardAgency.objects.all()
    )
    class Meta:
        model = MasRailComponentType
        fields = '__all__'

class MasRailwayLineSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasRailwayLine
        fields = '__all__'

class MasSubdistrictSerializer(serializers.ModelSerializer):
    masDistrict = MasDistrictSerializer(read_only=True)
    masDistrictId = serializers.PrimaryKeyRelatedField(
        source='masDistrict', 
        queryset=MasDistrict.objects.all()
    )
    class Meta:
        model = MasSubdistrict
        fields = '__all__'

class MasTrainTypeSerializer(serializers.ModelSerializer):
    masStandardAgency = MasStandardAgencySerializer(read_only=True)
    masStandardAgencyId = serializers.PrimaryKeyRelatedField(
        source='masStandardAgency', 
        queryset=MasStandardAgency.objects.all()
    )
    class Meta:
        model = MasTrainType
        fields = '__all__'

class MasTypeOfWorkSerializer(serializers.ModelSerializer):
    masStandardAgency = MasStandardAgencySerializer(read_only=True)
    masStandardAgencyId = serializers.PrimaryKeyRelatedField(
        source='masStandardAgency', 
        queryset=MasStandardAgency.objects.all()
    )
    class Meta:
        model = MasTypeOfWork
        fields = '__all__'

class MasUsagePurposeSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasUsagePurpose
        fields = '__all__'

class MasUserTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasUserType
        fields = '__all__'

class StandardSerializer(BaseModelSerializer):
    masStandardAgency = MasStandardAgencySerializer(read_only=True)
    masTrainType = MasTrainTypeSerializer(read_only=True)
    masRailComponentType = MasRailComponentTypeSerializer(read_only=True)
    masTypeOfWork = MasTypeOfWorkSerializer(read_only=True)
    
    # Custom field definitions to handle string inputs
    masStandardAgencyId = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    masTrainTypeId = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    masRailComponentTypeId = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    masTypeOfWorkId = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    
    def validate_masStandardAgencyId(self, value):
        if value in [None, "", "null"]:
            return None
        try:
            agency_id = int(value)
            MasStandardAgency.objects.get(pk=agency_id)
            return agency_id
        except ValueError:
            raise serializers.ValidationError("A valid integer is required")
        except MasStandardAgency.DoesNotExist:
            raise serializers.ValidationError("Standard agency with this ID does not exist")
    
    def validate_masTrainTypeId(self, value):
        if value in [None, "", "null"]:
            return None
        try:
            train_type_id = int(value)
            MasTrainType.objects.get(pk=train_type_id)
            return train_type_id
        except ValueError:
            raise serializers.ValidationError("A valid integer is required")
        except MasTrainType.DoesNotExist:
            raise serializers.ValidationError("Train type with this ID does not exist")
    
    def validate_masRailComponentTypeId(self, value):
        if value in [None, "", "null"]:
            return None
        try:
            component_id = int(value)
            MasRailComponentType.objects.get(pk=component_id)
            return component_id
        except ValueError:
            raise serializers.ValidationError("A valid integer is required")
        except MasRailComponentType.DoesNotExist:
            raise serializers.ValidationError("Rail component type with this ID does not exist")
    
    def validate_masTypeOfWorkId(self, value):
        if value in [None, "", "null"]:
            return None
        try:
            work_id = int(value)
            MasTypeOfWork.objects.get(pk=work_id)
            return work_id
        except ValueError:
            raise serializers.ValidationError("A valid integer is required")
        except MasTypeOfWork.DoesNotExist:
            raise serializers.ValidationError("Type of work with this ID does not exist")
    
    def create(self, validated_data):
        standard_agency_id = validated_data.pop('masStandardAgencyId', None)
        train_type_id = validated_data.pop('masTrainTypeId', None)
        rail_component_type_id = validated_data.pop('masRailComponentTypeId', None)
        type_of_work_id = validated_data.pop('masTypeOfWorkId', None)
        
        # Get related objects - note values have already been validated and converted to integers
        if standard_agency_id is not None:
            validated_data['masStandardAgency'] = MasStandardAgency.objects.get(pk=standard_agency_id)
        if train_type_id is not None:
            validated_data['masTrainType'] = MasTrainType.objects.get(pk=train_type_id)
        if rail_component_type_id is not None:
            validated_data['masRailComponentType'] = MasRailComponentType.objects.get(pk=rail_component_type_id)
        if type_of_work_id is not None:
            validated_data['masTypeOfWork'] = MasTypeOfWork.objects.get(pk=type_of_work_id)
                
        return Standard.objects.create(**validated_data)
    
    def update(self, instance, validated_data):
        # Handle related fields
        if 'masStandardAgencyId' in validated_data:
            standard_agency_id = validated_data.pop('masStandardAgencyId')
            if standard_agency_id is None:
                instance.masStandardAgency = None
            else:
                instance.masStandardAgency = MasStandardAgency.objects.get(pk=standard_agency_id)
        
        if 'masTrainTypeId' in validated_data:
            train_type_id = validated_data.pop('masTrainTypeId')
            if train_type_id is None:
                instance.masTrainType = None
            else:
                instance.masTrainType = MasTrainType.objects.get(pk=train_type_id)
                
        if 'masRailComponentTypeId' in validated_data:
            rail_component_type_id = validated_data.pop('masRailComponentTypeId')
            if rail_component_type_id is None:
                instance.masRailComponentType = None
            else:
                instance.masRailComponentType = MasRailComponentType.objects.get(pk=rail_component_type_id)
                
        if 'masTypeOfWorkId' in validated_data:
            type_of_work_id = validated_data.pop('masTypeOfWorkId')
            if type_of_work_id is None:
                instance.masTypeOfWork = None
            else:
                instance.masTypeOfWork = MasTypeOfWork.objects.get(pk=type_of_work_id)
        
        # Update other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
            
        instance.save()
        return instance
        
    class Meta:
        model = Standard
        fields = '__all__'

class StandardMasStandardCategorySerializer(serializers.ModelSerializer):
    standard = StandardSerializer(read_only=True)
    masStandardCategory = MasStandardCategorySerializer(read_only=True)
    standardId = serializers.PrimaryKeyRelatedField(
        source='standard', 
        queryset=Standard.objects.all()
    )
    masStandardCategoryId = serializers.PrimaryKeyRelatedField(
        source='masStandardCategory', 
        queryset=MasStandardCategory.objects.all()
    )
    
    class Meta:
        model = StandardMasStandardCategory
        fields = '__all__'
