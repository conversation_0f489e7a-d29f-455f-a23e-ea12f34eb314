from rest_framework import serializers
from users.models import User

class UserInfoSerializer(serializers.ModelSerializer):
    """
    Serializer for displaying basic user information
    """
    class Meta:
        model = User
        fields = ['id', 'firstname', 'lastname', 'email', 'userType']


class BaseModelSerializer(serializers.ModelSerializer):
    """
    Base serializer for all models that inherit from BaseModel
    Automatically adds user information for create_user and update_user
    """
    createUser = serializers.SerializerMethodField()
    updateUser = serializers.SerializerMethodField()
    
    def get_createUser(self, obj):
        user = obj.create_user
        if user:
            return {
                'id': user.id,
                'name': f"{user.firstname} {user.lastname}",
                'email': user.email,
                'phoneNumber': user.phoneNumber,
                'userType': 'เจ้าหน้าที่' if user.userType.lower() == 'o' else 'สมาชิก'
            }
        return None
    
    def get_updateUser(self, obj):
        user = obj.update_user
        if user:
            return {
                'id': user.id,
                'name': f"{user.firstname} {user.lastname}",
                'email': user.email,
                'phoneNumber': user.phoneNumber,
                'userType': 'เจ้าหน้าที่' if user.userType.lower() == 'o' else 'สมาชิก'
            }
        return None 