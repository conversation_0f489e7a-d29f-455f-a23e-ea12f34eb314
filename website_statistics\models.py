from django.db import models

class Websitestatistics(models.Model):
    id = models.AutoField(primary_key=True) 
    page = models.CharField(max_length=200) 
    ipAddress = models.CharField(max_length=50, blank=True, null=True) 
    browser = models.CharField(max_length=100, blank=True, null=True) 
    version = models.CharField(max_length=100, blank=True, null=True) 
    platform = models.CharField(max_length=500, blank=True, null=True) 
    createDate = models.DateTimeField(auto_now_add=True) 

    class Meta:
        managed = False
        db_table = 'WebsiteStatistics'
        
class WebsiteStatisticsChartData(models.Model):
    label = []
    data = []
    
    def __str__(self):
        return self

