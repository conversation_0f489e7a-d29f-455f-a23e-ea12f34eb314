from rest_framework import serializers
from .models import OwnerProject, OwnerProjectMasRailwayLine, MasRailwayLine
from mas.serializers import MasTrainTypeSerializer, MasRailwayLineSerializer
from RTRDA.serializers import BaseModelSerializer

class OwnerProjectSerializer(BaseModelSerializer):
    masTrainType = MasTrainTypeSerializer(read_only=True)
    masTrainTypeId = serializers.PrimaryKeyRelatedField(
        source='masTrainType',
        queryset=MasTrainTypeSerializer.Meta.model.objects.all()
    )
    masRailwayLineId = serializers.CharField(
        write_only=True,
        required=False
    )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        # Get the instance ID, either directly from model instance or from dict
        instance_id = instance.id if hasattr(instance, 'id') else representation.get('id')
        
        if instance_id:
            mas_railway_lines = OwnerProjectMasRailwayLine.objects.filter(ownerProject=instance_id)
            railway_line_ids = mas_railway_lines.values_list('masRailwayLine', flat=True)
            railway_lines = MasRailwayLine.objects.filter(id__in=railway_line_ids)
            representation['masRailwayLine'] = MasRailwayLineSerializer(railway_lines, many=True).data
        
        return representation

    def create(self, validated_data):
        # Get railway line IDs from comma-separated string
        railway_line_str = validated_data.pop('masRailwayLineId', '')
        mas_railway_line_ids = []
        
        if railway_line_str:
            # Split by comma and convert to integers
            try:
                mas_railway_line_ids = [int(id.strip()) for id in railway_line_str.split(',') if id.strip()]
            except ValueError:
                # Handle invalid inputs gracefully
                pass
            
        # Debug print
        print("Railway line IDs:", mas_railway_line_ids)
        
        instance = super().create(validated_data)
        
        # Create railway line relationships
        for railway_line_id in mas_railway_line_ids:
            if railway_line_id:  # Skip empty values
                OwnerProjectMasRailwayLine.objects.create(
                    ownerProject=instance,
                    masRailwayLine_id=railway_line_id
                )
        
        # Refresh from database to get the latest data
        instance.refresh_from_db()
        return instance

    def update(self, instance, validated_data):
        # Get railway line IDs from comma-separated string
        railway_line_str = validated_data.pop('masRailwayLineId', None)
        
        if railway_line_str is not None:
            mas_railway_line_ids = []
            
            if railway_line_str:
                # Split by comma and convert to integers
                try:
                    mas_railway_line_ids = [int(id.strip()) for id in railway_line_str.split(',') if id.strip()]
                except ValueError:
                    # Handle invalid inputs gracefully
                    pass
            
            instance = super().update(instance, validated_data)
            
            # Remove existing relationships
            OwnerProjectMasRailwayLine.objects.filter(ownerProject=instance).delete()
            
            # Create new relationships
            for railway_line_id in mas_railway_line_ids:
                if railway_line_id:  # Skip empty values
                    OwnerProjectMasRailwayLine.objects.create(
                        ownerProject=instance,
                        masRailwayLine_id=railway_line_id
                    )
        else:
            instance = super().update(instance, validated_data)
        
        # Refresh from database to get the latest data
        instance.refresh_from_db()
        return instance

    class Meta:
        model = OwnerProject
        fields = '__all__'

class OwnerProjectMasRailwayLineSerializer(serializers.ModelSerializer):
    ownerProject = OwnerProjectSerializer(read_only=True)
    masRailwayLine = MasRailwayLineSerializer(read_only=True)
    ownerProjectId = serializers.PrimaryKeyRelatedField(
        source='ownerProject',
        queryset=OwnerProjectSerializer.Meta.model.objects.all()
    )
    masRailwayLineId = serializers.PrimaryKeyRelatedField(
        source='masRailwayLine',
        queryset=MasRailwayLineSerializer.Meta.model.objects.all()
    )

    class Meta:
        model = OwnerProjectMasRailwayLine
        fields = '__all__' 