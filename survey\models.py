from django.db import models
from mas.models import MasAgeRange, MasUserType

class Survey(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=500, db_collation='Thai_CI_AI')
    masAgeRange = models.ForeignKey(MasAgeRange, on_delete=models.PROTECT, db_column='MasAgeRangeId')
    masUserType = models.ForeignKey(MasUserType, on_delete=models.PROTECT, db_column='MasUserTypeId')
    departmentName = models.CharField(db_column='DepartmentName', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=500, db_collation='Thai_CI_AI')
    isObjectiveStatistic = models.BooleanField(db_column='IsObjectiveStatistic')
    isObjectiveRoute = models.BooleanField(db_column='IsObjectiveRoute')
    isObjectiveUnderConstruction = models.BooleanField(db_column='IsObjectiveUnderConstruction')
    isObjectiveSafety = models.BooleanField(db_column='IsObjectiveSafety')
    isObjectiveIndustrial = models.BooleanField(db_column='IsObjectiveIndustrial')
    isObjectiveEquipment = models.BooleanField(db_column='IsObjectiveEquipment')
    isObjectiveStandard = models.BooleanField(db_column='IsObjectiveStandard')
    isObjectiveTestingCenter = models.BooleanField(db_column='IsObjectiveTestingCenter')
    isObjectiveRailwayIndustry = models.BooleanField(db_column='IsObjectiveRailwayIndustry')
    isObjectiveMaintenance = models.BooleanField(db_column='IsObjectiveMaintenance')
    isObjectiveEnvironment = models.BooleanField(db_column='IsObjectiveEnvironment')
    isObjectiveResearch = models.BooleanField(db_column='IsObjectiveResearch')
    isObjectiveCourse = models.BooleanField(db_column='IsObjectiveCourse')
    isObjectiveTraning = models.BooleanField(db_column='IsObjectiveTraning')
    isObjectiveNews = models.BooleanField(db_column='IsObjectiveNews')
    isObjectiveOther = models.BooleanField(db_column='IsObjectiveOther')
    objectiveOther = models.CharField(db_column='ObjectiveOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    isAccessDashboard = models.BooleanField(db_column='IsAccessDashboard')
    isAccessMap = models.BooleanField(db_column='IsAccessMap')
    isAccessDigitalData = models.BooleanField(db_column='IsAccessDigitalData')
    isAccessDocument = models.BooleanField(db_column='IsAccessDocument')
    isAccessOther = models.BooleanField(db_column='IsAccessOther')
    accessOther = models.CharField(db_column='AccessOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    isUpdateRealTime = models.BooleanField(db_column='IsUpdateRealTime')
    isUpdateDaily = models.BooleanField(db_column='IsUpdateDaily')
    isUpdateMonthly = models.BooleanField(db_column='IsUpdateMonthly')
    isUpdateYearly = models.BooleanField(db_column='IsUpdateYearly')
    isSupportInformationTracking = models.BooleanField(db_column='IsSupportInformationTracking')
    isSupportBusinessAnalysis = models.BooleanField(db_column='IsSupportBusinessAnalysis')
    isSupportPolicyFormulation = models.BooleanField(db_column='IsSupportPolicyFormulation')
    isSupportResearch = models.BooleanField(db_column='IsSupportResearch')
    isSupportOther = models.BooleanField(db_column='IsSupportOther')
    supportOther = models.CharField(db_column='SupportOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    isChannelWebsite = models.BooleanField(db_column='IsChannelWebsite')
    isChannelApplication = models.BooleanField(db_column='IsChannelApplication')
    isChannelAPI = models.BooleanField(db_column='IsChannelAPI')
    isChannelContact = models.BooleanField(db_column='IsChannelContact')
    isChannelOther = models.BooleanField(db_column='IsChannelOther')
    channelOther = models.CharField(db_column='ChannelOther', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    createDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'Survey'

class SurveyChartData(models.Model):
    label = []
    data = []
    
    def __str__(self):
        return self
