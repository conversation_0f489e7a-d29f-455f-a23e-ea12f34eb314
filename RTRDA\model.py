from django.db import models
from django.utils import timezone
from RTRDA.middleware import get_current_user_id

class BaseModel(models.Model):
    id = models.AutoField(primary_key=True)
    createUserId = models.IntegerField(blank=True, null=True)
    createDate = models.DateTimeField(auto_now_add=True)
    updateUserId = models.IntegerField(blank=True, null=True)
    updateDate = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        abstract = True
        app_label = 'RTRDA'
        
    def save(self, *args, **kwargs):
        user_id = get_current_user_id()
        if not self.pk and user_id:
            self.createUserId = user_id
        elif self.pk and user_id:
            self.updateUserId = user_id
            self.updateDate = timezone.now()
        super().save(*args, **kwargs)
    
    @property
    def create_user(self):
        """
        Returns the user who created this record
        """
        if not self.createUserId:
            return None
        # Lazy import to avoid circular dependency
        from users.models import User
        return User.objects.filter(id=self.createUserId).first()
    
    @property
    def update_user(self):
        """
        Returns the user who last updated this record
        """
        if not self.updateUserId:
            return None
        # Lazy import to avoid circular dependency
        from users.models import User
        return User.objects.filter(id=self.updateUserId).first()
    
    def get_user_fullname(self, user_type='create'):
        """
        Returns the full name of the user who created or updated this record
        Args:
            user_type: 'create' or 'update'
        """
        user = None
        if user_type == 'create':
            user = self.create_user
        elif user_type == 'update':
            user = self.update_user
            
        if user:
            return f"{user.firstname} {user.lastname}"
        return None

