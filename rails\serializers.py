from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import RailServiceProvider, RailServiceProviderMasRailwayLine, RailwayEngineeringStandard, AgencySupportRailwayResearch, UnderConstruction
from mas.serializers import MasTrainTypeSerializer, MasRailwayLineSerializer
from mas.models import MasTrainType, MasRailwayLine


class RailServiceProviderSerializer(BaseModelSerializer):
    masTrainType = MasTrainTypeSerializer(read_only=True)
    masTrainTypeId = serializers.PrimaryKeyRelatedField(
        source='masTrainType',
        queryset=MasTrainType.objects.all()
    )
    masRailwayLines = serializers.ListField(child=serializers.JSONField(), read_only=True, required=False)

    class Meta:
        model = RailServiceProvider
        fields = '__all__'
        
class UpdateRailServiceProviderSerializer(serializers.Serializer):
    masRailwayLines = serializers.ListField(child=serializers.IntegerField(), required=True)


class RailServiceProviderMasRailwayLineSerializer(serializers.ModelSerializer):
    masRailwayLine = MasRailwayLineSerializer(read_only=True)
    railServiceProvider = RailServiceProviderSerializer(read_only=True)
    masRailwayLineId = serializers.PrimaryKeyRelatedField(
        source='masRailwayLine',
        queryset=MasRailwayLine.objects.all()
    )
    railServiceProviderId = serializers.PrimaryKeyRelatedField(
        source='railServiceProvider',
        queryset=RailServiceProvider.objects.all()
    )

    class Meta:
        model = RailServiceProviderMasRailwayLine
        fields = '__all__'


class RailwayEngineeringStandardSerializer(BaseModelSerializer):
    class Meta:
        model = RailwayEngineeringStandard
        fields = '__all__'


class AgencySupportRailwayResearchSerializer(BaseModelSerializer):
    class Meta:
        model = AgencySupportRailwayResearch
        fields = '__all__'


class UnderConstructionSerializer(BaseModelSerializer):
    masRailwayLine = MasRailwayLineSerializer(read_only=True)
    masRailwayLineId = serializers.PrimaryKeyRelatedField(
        source='masRailwayLine',
        queryset=MasRailwayLine.objects.all()
    )

    class Meta:
        model = UnderConstruction
        fields = '__all__'
