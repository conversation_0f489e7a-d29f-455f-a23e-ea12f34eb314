from mas.models import Standard
from mas.serializers import StandardSerializer
from testings.models import Testing
from testings.serializers import TestingSerializer
from .models import SearchModel
from .serializers import SearchSerializer
from utils.pagination import CustomPagination
from rest_framework.permissions import AllowAny
from rest_framework import viewsets
from drf_spectacular.utils import extend_schema
from django.db.models import Q
from manufacturer.models import Manufacturer
from manufacturer.serializers import ManufacturerSerializer
from research.models import Research
from research.serializers import ResearchSerializer
from course.models import Course
from course.serializers import CourseSerializer
from news.models import News
from news.serializers import NewsSerializer
from trainings.models import Training
from trainings.serializers import TrainingSerializer
from django.db import connection
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.response import Response
from rest_framework.decorators import action
import requests
import json
from django.core.cache import cache
from django.conf import settings
import logging

# Get an instance of a logger
logger = logging.getLogger(__name__)

@extend_schema(
    tags=["Search"]
)
class SearchViewSet(viewsets.ModelViewSet):
    serializer_class = SearchSerializer
    pagination_class = CustomPagination
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['pageName', 'search']
    search_fields = ['pageName', 'search']

    def list(self, request, *args, **kwargs):
        """
        <h1>ค้นหาข้อมูลจากหน้าเว็บ</h1>
        <p>Parameter:</p>
        <ul>
            <li>pageName : หน้าเว็บที่ต้องการค้นหา</li>
            <ul>
                <li>Standard</li>
                <li>TestingCenter</li>
                <li>Manufacturer</li>
                <li>Research</li>
                <li>Course</li>
                <li>News</li>
                <li>Training</li>
            </ul>
            <li>search : คีย์เวิร์ดที่ต้องการค้นหา</li>
        </ul>
        <h2>ตัวอย่างการใช้งาน</h2>
        <p>ค้นหาข้อมูลจากหน้าเว็บ "ข่าวสาร" ที่มีคีย์เวิร์ด "การประชุม"</p>
        <p>ตัวอย่าง URL: /api/search/?page=news&search=การประชุม</p>
        """
        pageName = request.query_params.get('pageName')
        search = request.query_params.get('search')
        status = True
        results = []

        # Standard Data
        if not pageName or pageName == 'Standard':
            standardQueryset = Standard.objects.filter(status=status)
            if search:
                standardQueryset = standardQueryset.filter(
                    Q(name__icontains=search) | Q(detail__icontains=search)
                )
            standardSerializer = StandardSerializer(
                standardQueryset, many=True)
            for standard in standardSerializer.data:
                search_model = SearchModel()
                search_model.page = pageName
                search_model.pageName = 'ข้อมูลมาตรฐานระบบราง'
                search_model.detail = standard['code']
                search_model.title = standard['name']
                search_model.id = standard['id']
                results.append(self.get_serializer(search_model).data)

        # Testing Data
        if not pageName or pageName == 'TestingCenter':
            with connection.cursor() as cursor:
                base_query = """
                    select tc.Name, tca.Name, tc.id
                    from TestingCenter tc
                    join TestingCenterAgency tca on tc.TestingCenterAgencyId = tca.Id
                """
                search_param = f'%{search}%'
                if search:
                    base_query += " WHERE tc.Name LIKE %s OR tca.Name LIKE %s "
                    cursor.execute(base_query, [search_param, search_param])
                else:
                    cursor.execute(base_query)
                rows = cursor.fetchall()

            for testing in rows:
                search_model = SearchModel()
                search_model.page = pageName
                search_model.pageName = 'ข้อมูลศูนย์ทดสอบ'
                search_model.detail = testing[1]
                search_model.title = testing[0]
                search_model.id = testing[2]
                results.append(self.get_serializer(search_model).data)

        # Manufacturer Data
        if not pageName or pageName == 'Manufacturer':
            with connection.cursor() as cursor:
                base_query = """
                    select m.Name, (m.Address + ' ' + msd.Name + ' ' + md.Name + ' ' + mp.Name + ' ' + m.Zipcode) as address, m.id
                    from Manufacturer m
                    join MasProvince mp on m.MasProvinceId = mp.id
                    join MasDistrict md on m.MasDistrictId = md.id
                    join MasSubdistrict msd on m.MasSubdistrictId = msd.id
                """
                search_param = f'%{search}%'
                if search:
                    base_query += " WHERE m.Name LIKE %s OR address LIKE %s "
                    cursor.execute(base_query, [search_param, search_param])
                else:
                    cursor.execute(base_query)
                rows = cursor.fetchall()
            for manufacturer in rows:
                search_model = SearchModel()
                search_model.page = pageName
                search_model.pageName = 'ข้อมูลผู้ผลิต/โรงงาน'
                search_model.detail = manufacturer[1]
                search_model.title = manufacturer[0]
                search_model.id = manufacturer[2]
                results.append(self.get_serializer(search_model).data)

        # Research Data
        if not pageName or pageName == 'Research':
            with connection.cursor() as cursor:
                base_query = """
                    select r.ProjectName, rs.Name + ' - ' + ra.Name, r.id
                    from ResearchResearcher rr
                    join Research r on rr.ResearchId = r.id
                    join Researcher rs on rr.ResearcherId = rs.id
                    join ResearchResearchAgency rra on r.id = rra.ResearchId
                    join ResearchAgency ra on rra.ResearchAgencyId = ra.Id
                """
                search_param = f'%{search}%'
                if search:
                    base_query += " WHERE r.ProjectName LIKE %s OR r.ProjectCode LIKE %s OR rs.Name LIKE %s OR ra.Name LIKE %s "
                    cursor.execute(
                        base_query, [search_param, search_param, search_param, search_param])
                else:
                    cursor.execute(base_query)
                rows = cursor.fetchall()
            for research in rows:
                search_model = SearchModel()
                search_model.page = pageName
                search_model.pageName = 'ข้อมูลงานวิจัยและนวัตกรรม'
                search_model.detail = research[1]
                search_model.title = research[0]
                search_model.id = research[2]
                results.append(self.get_serializer(search_model).data)

        # Course Data
        if not pageName or pageName == 'Course':
            courseQueryset = Course.objects.filter(status=status)
            if search:
                courseQueryset = courseQueryset.filter(
                    Q(programTh__icontains=search) | Q(programEn__icontains=search) | Q(
                        universityName__icontains=search) | Q(facultyName__icontains=search) | Q(department__icontains=search)
                )
            courseSerializer = CourseSerializer(courseQueryset, many=True)
            for course in courseSerializer.data:
                search_model = SearchModel()
                search_model.page = pageName
                search_model.pageName = 'ข้อมูลหลักสูตร'
                search_model.detail = course['facultyName'] + ' - ' + course['programTh'] + ' ' + course['department']
                search_model.title = course['universityName']
                search_model.id = course['id']
                results.append(self.get_serializer(search_model).data)

        # News Data
        if not pageName or pageName == 'News':
            newsQueryset = News.objects.filter(status=status)
            if search:
                newsQueryset = newsQueryset.filter(
                    Q(name__icontains=search) | Q(detail__icontains=search)
                )
            newsSerializer = NewsSerializer(newsQueryset, many=True)
            for news in newsSerializer.data:
                search_model = SearchModel()
                search_model.page = pageName
                search_model.pageName = 'ข้อมูลข่าวสารและกิจกรรม'
                search_model.detail = news['detail']
                search_model.title = news['name']
                search_model.id = news['id']
                results.append(self.get_serializer(search_model).data)

        # Training Data
        if not pageName or pageName == 'Training':
            trainingQueryset = Training.objects.filter(status=status)
            if search:
                trainingQueryset = trainingQueryset.filter(
                    Q(name__icontains=search) | Q(detail__icontains=search)
                )
            trainingSerializer = TrainingSerializer(trainingQueryset, many=True)
            for training in trainingSerializer.data:
                search_model = SearchModel()
                search_model.page = pageName
                search_model.pageName = 'ข้อมูลการฝึกอบรม'
                search_model.detail = training['detail']
                search_model.title = training['name']
                search_model.id = training['id']
                results.append(self.get_serializer(search_model).data)

        page = self.paginate_queryset(results)
        return self.get_paginated_response(page)

    @action(detail=False, methods=['get'], url_path='drt-data')
    @extend_schema(
        summary="Fetch DRT Crossing Data",
        description="Retrieves railway crossing data from the DRT (Department of Rail Transport) API and returns it as GeoJSON.",
        responses={
            200: {"type": "object", "description": "GeoJSON data containing railway crossing information"},
            500: {"type": "object", "description": "Error response when API request fails"}
        },
        tags=["External Data"],
    )
    def drt_data(self, request):
        """
        Fetch crossing data from DRT API and return as JSON
        
        This endpoint fetches GeoJSON data from the DRT crossing API and returns it directly.
        The data contains information about railway crossings in Thailand.
        
        Query Parameters:
        - layer: Layer to fetch from DRT service (crossing, accident, masterplan)
        """
        # Get parameters from request with defaults
        key = settings.DRT_API_KEY
        layer = request.query_params.get('layer')
        
        # Validate parameters
        if not key:
            logger.warning("DRT data request with empty API key")
            return Response({
                "error": "Missing required parameter",
                "message": "API key cannot be empty"
            }, status=400)
            
        if not layer:
            logger.warning("DRT data request with empty layer")
            return Response({
                "error": "Missing required parameter",
                "message": "Layer cannot be empty"
            }, status=400)
        
        # Create a unique cache key based on the parameters
        cache_key = f'drt_data_{layer}_{key}'
        
        try:
            # API endpoint URL with the parameters
            url = f'https://crossing.drt.go.th/drt/service/geojson.ashx?key={key}&layer={layer}'
            
            logger.info(f"Fetching DRT data from external API for layer: {layer}")
            
            # Make the request to the DRT API
            response = requests.get(url, timeout=30)
            
            # Check if the request was successful
            if response.status_code == 200:
                try:
                    # Parse the JSON response
                    data = response.json()
                    
                    # Log the number of features if it's a GeoJSON
                    if 'features' in data and isinstance(data['features'], list):
                        logger.info(f"Retrieved {len(data['features'])} features from DRT API for layer: {layer}")
                    
                    logger.info(f"DRT data cached for layer: {layer}")
                    
                    return Response(data)
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON response from DRT API for layer: {layer}")
                    return Response({
                        "error": "Invalid JSON response from DRT API",
                        "message": "The API returned a non-JSON response"
                    }, status=502)
            elif response.status_code == 401 or response.status_code == 403:
                logger.error(f"Authentication failed for DRT API request. Status code: {response.status_code}")
                return Response({
                    "error": "Authentication failed",
                    "message": "Invalid API key or unauthorized access"
                }, status=response.status_code)
            else:
                logger.error(f"Failed to fetch data from DRT API. Status code: {response.status_code}, Layer: {layer}")
                return Response({
                    "error": f"Failed to fetch data from DRT API. Status code: {response.status_code}",
                    "message": response.text
                }, status=response.status_code)
                
        except requests.exceptions.Timeout:
            logger.error(f"Request to DRT API timed out for layer: {layer}")
            return Response({
                "error": "Request to DRT API timed out",
                "message": "The external API is taking too long to respond"
            }, status=504)  # Gateway Timeout
        except requests.exceptions.RequestException as e:
            logger.error(f"Request to DRT API failed for layer: {layer}. Error: {str(e)}")
            return Response({
                "error": "Request to DRT API failed",
                "message": str(e)
            }, status=502)  # Bad Gateway
        except Exception as e:
            logger.exception(f"Unexpected error while fetching DRT data for layer: {layer}")
            return Response({
                "error": "An error occurred while fetching data from DRT API",
                "message": str(e)
            }, status=500)
