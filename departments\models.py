from django.db import models
from RTRDA.model import BaseModel

class Department(BaseModel):
    name = models.CharField(db_column='Name', max_length=200) 
    logo = models.ImageField(db_column='Logo', upload_to='department/', blank=True, null=True) 

    class Meta:
        managed = False
        db_table = 'Department'
        

class ExternalData(models.Model):
    department = models.ForeignKey(Department, on_delete=models.PROTECT, db_column='DepartmentId', blank=True, null=True)
    type = models.CharField(db_column='Type', max_length=200, blank=True, null=True)
    data = models.TextField(db_column='Data', blank=True, null=True)
    createdate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'ExternalData'
