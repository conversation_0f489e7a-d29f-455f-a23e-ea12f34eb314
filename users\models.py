from django.db import models
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager
from django.contrib.auth.hashers import make_password, check_password
import hashlib
from RTRDA.middleware import get_current_user_id
from django.utils import timezone
from RTRDA.model import BaseModel


class UserManager(BaseUserManager):
    def create_user(self, email, emailConfirmed=1, phoneNumberConfirmed=1, twoFactorEnabled=0, lockoutEnabled=1, accessFailedCount=0, status=True, password=None, firstname=None, lastname=None, **extra_fields):
        if not email:
            raise ValueError("Email is required")
        email = self.normalize_email(email)
        
        # Create user without password first
        user = self.model(email=email, emailConfirmed=emailConfirmed, phoneNumberConfirmed=phoneNumberConfirmed, twoFactorEnabled=twoFactorEnabled,
                          lockoutEnabled=lockoutEnabled, accessFailedCount=accessFailedCount, status=status, firstname=firstname, lastname=lastname, **extra_fields)
        
        # Set password directly if provided
        if password:
            # Hash the password and set it directly to the passwordHash field
            user.passwordHash = hashlib.md5(password.encode()).hexdigest()
            # Store raw password for potential later use
            user._raw_password = password
        
        user.save()
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault("userType", 'A')
        return self.create_user(email=email, password=password, **extra_fields)


class User(AbstractBaseUser):
    id = models.AutoField(db_column='Id', primary_key=True)
    userType = models.CharField(db_column='UserType', max_length=1, db_collation='Thai_CI_AI',default='O', blank=True, null=True)
    firstname = models.CharField(db_column='Firstname', max_length=100, db_collation='Thai_CI_AI')
    lastname = models.CharField(db_column='Lastname', max_length=100, db_collation='Thai_CI_AI')
    passwordHash = models.CharField(db_column='PasswordHash', max_length=250, db_collation='Thai_CI_AI', null=True)
    email = models.CharField(db_column='Email', max_length=100, db_collation='Thai_CI_AI', unique=True)
    phoneNumber = models.CharField(db_column='PhoneNumber', max_length=10, db_collation='Thai_CI_AI')
    image = models.ImageField(db_column='Image', upload_to='user/img/', blank=True, null=True)
    securityStamp = models.CharField(db_column='SecurityStamp', max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)
    emailConfirmed = models.BooleanField(db_column='EmailConfirmed', default=True)
    phoneNumberConfirmed = models.BooleanField(db_column='PhoneNumberConfirmed', default=True)
    twoFactorEnabled = models.BooleanField(db_column='TwoFactorEnabled', default=False)
    lockoutEndDateUtc = models.DateTimeField(db_column='LockoutEndDateUtc', blank=True, null=True)
    lockoutEnabled = models.BooleanField(db_column='LockoutEnabled', default=True)
    accessFailedCount = models.IntegerField(db_column='AccessFailedCount', default=0)
    status = models.BooleanField(db_column='Status', default=True)
    createUserId = models.IntegerField(db_column='CreateUserId', blank=True, null=True)
    createDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)
    updateUserId = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)
    updateDate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)

    objects = UserManager()

    USERNAME_FIELD = "email"
    EMAIL_FIELD = "email"
    REQUIRED_FIELDS = []

    class Meta:
        managed = False
        db_table = 'User'

    def __str__(self):
        return self.email
      
    def save(self, *args, **kwargs):
        user_id = get_current_user_id()
        if not self.pk and user_id:
            self.createUserId = user_id
        elif self.pk and user_id:
            self.updateUserId = user_id
            self.updateDate = timezone.now()
        super().save(*args, **kwargs)
        
    @property
    def is_staff(self):
        # Assuming 'A' is for admin in your userType field
        return self.userType == 'A'

    @property
    def is_admin(self):
        # Assuming 'A' is for admin in your userType field
        return self.userType == 'A'

    @property
    def is_active(self):
        return self.status

    @property
    def last_login(self):
        # Return None since we don't have this field in the database
        return None

    @last_login.setter
    def last_login(self, value):
        # Do nothing since we don't have this field in the database
        pass

    # Override AbstractBaseUser's password property
    @property
    def password(self):
        # This is just a getter, it should return the raw password if available
        return getattr(self, '_raw_password', None)

    @password.setter
    def password(self, raw_password):
        # Store the raw password temporarily
        self._raw_password = raw_password
        # Hash the password and store it in the passwordHash field
        if raw_password:
            self.passwordHash = hashlib.md5(raw_password.encode()).hexdigest()
        else:
            self.passwordHash = None

    def set_password(self, raw_password):
        # Just use the password setter
        self.password = raw_password

    def check_password(self, raw_password):
        """
        Return a boolean of whether the raw_password was correct. Handles
        hashing formats behind the scenes.
        """
        # Check if the passwordHash is in MD5 format
        if self.passwordHash and len(self.passwordHash) == 32:
            # MD5 hash the raw password
            md5_hash = hashlib.md5(raw_password.encode()).hexdigest()
            return md5_hash.lower() == self.passwordHash.lower()

        # Fall back to Django's check_password for other formats
        def setter(raw_password):
            self.set_password(raw_password)
            self.save(update_fields=["passwordHash"])
        return check_password(raw_password, self.passwordHash, setter)

    # Add these methods to replace PermissionsMixin functionality
    def has_perm(self, perm, obj=None):
        return self.is_admin

    def has_module_perms(self, app_label):
        return self.is_admin

    def get_group_permissions(self):
        return set()

    def get_all_permissions(self):
        return set()


class Userlog(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    user = models.ForeignKey(User, on_delete=models.PROTECT, db_column='UserId')
    header = models.CharField(db_column='Header', max_length=500, db_collation='Thai_CI_AI')
    detail = models.CharField(db_column='Detail', max_length=500, db_collation='Thai_CI_AI')
    ipAddress = models.CharField(db_column='IpAddress', max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    browser = models.CharField(db_column='Browser', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    version = models.CharField(db_column='Version', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    platform = models.CharField(db_column='Platform', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    createDate = models.DateTimeField(db_column='CreateDate',auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'UserLog'
        

class Role(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=100, db_collation='Thai_CI_AI')
    descriptionTh = models.CharField(db_column='DescriptionTh', max_length=100, db_collation='Thai_CI_AI')
    descriptionEn = models.CharField(db_column='DescriptionEn', max_length=100, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'Role'


class UserRole(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    user = models.ForeignKey(User, on_delete=models.PROTECT, db_column='UserId')
    role = models.ForeignKey(Role, on_delete=models.PROTECT, db_column='RoleId')

    class Meta:
        managed = False
        db_table = 'UserRole'
        unique_together = (('role', 'user'),)


class Permission(BaseModel):
    name = models.CharField(db_column='Name', max_length=250, db_collation='Thai_CI_AI')
    description = models.CharField(db_column='Description', max_length=250, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Permission'


class PermissionRole(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    role = models.ForeignKey(Role, on_delete=models.CASCADE, db_column='RoleId')
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE, db_column='PermissionId')
    roles = []

    class Meta:
        managed = False
        db_table = 'PermissionRole'
        unique_together = (('permission', 'role'),)


class PermissionUser(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    user = models.ForeignKey(User, on_delete=models.PROTECT, db_column='UserId')
    permission = models.ForeignKey(Permission, on_delete=models.PROTECT, db_column='PermissionId')

    class Meta:
        managed = False
        db_table = 'PermissionUser'
        unique_together = (('permission', 'user'),)
