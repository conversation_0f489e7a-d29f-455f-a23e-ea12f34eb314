from rest_framework import serializers
from .models import Training, TrainingCategory
from RTRDA.serializers import BaseModelSerializer

class TrainingCategorySerializer(BaseModelSerializer):
    class Meta:
        model = TrainingCategory
        fields = '__all__'

class TrainingSerializer(BaseModelSerializer):
    trainingCategory = TrainingCategorySerializer(read_only=True)
    trainingCategoryId = serializers.PrimaryKeyRelatedField(
        source='trainingCategory', 
        queryset=TrainingCategory.objects.all()
    )
    class Meta:
        model = Training
        fields = '__all__'