from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.conf import settings

class Versioned<PERSON><PERSON><PERSON><PERSON><PERSON>(JSONRenderer):
    """
    A custom JSON renderer that adds API_VERSION to all responses.
    """
    
    def render(self, data, accepted_media_type=None, renderer_context=None):
        if data is not None and isinstance(data, dict):
            # Add API_VERSION to the response data
            data['version'] = settings.API_VERSION
        
        # Call the parent class's render method
        return super().render(data, accepted_media_type, renderer_context) 