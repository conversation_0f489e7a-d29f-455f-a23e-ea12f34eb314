from django.db import models
from RTRDA.model import BaseModel
from mas.models import MasCourseType

class Course(BaseModel):
    universityName = models.CharField(db_column='UniversityName', max_length=200, db_collation='Thai_CI_AI')
    facultyName = models.CharField(db_column='FacultyName', max_length=200, db_collation='Thai_CI_AI')
    department = models.CharField(db_column='Department', max_length=200, db_collation='Thai_CI_AI')
    programTh = models.Char<PERSON>ield(db_column='ProgramTh', max_length=200, db_collation='Thai_CI_AI')
    programEn = models.CharField(db_column='ProgramEn', max_length=200, db_collation='Thai_CI_AI')
    masCourseType = models.ForeignKey(MasCourseType, on_delete=models.PROTECT, db_column='MasCourseTypeId')
    detail = models.Char<PERSON><PERSON>(db_column='Detail', max_length=1000, db_collation='Thai_CI_AI')
    numberOfStudent = models.CharField(db_column='NumberOfStudent', max_length=10, db_collation='Thai_CI_AI')
    link = models.CharField(db_column='Link', max_length=1000, db_collation='Thai_CI_AI')
    contactName = models.CharField(db_column='ContactName', max_length=300, db_collation='Thai_CI_AI')
    contactEmail = models.CharField(db_column='ContactEmail', max_length=100, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status')
    views = models.IntegerField(db_column='Views', default=0)

    class Meta:
        managed = False
        db_table = 'Course'
