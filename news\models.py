import os
from django.db import models
from RTRDA.model import BaseModel

class NewsCategory(BaseModel):
    name = models.CharField(max_length=200)

    class Meta:
        managed = False
        db_table = 'NewsCategory'

    def __str__(self):
        return self.name


class News(BaseModel):
    name = models.CharField(max_length=250)
    newsCategory = models.ForeignKey(
        NewsCategory,
        on_delete=models.PROTECT,
        db_column='newsCategoryId')
    detail = models.TextField()
    cover = models.ImageField(upload_to='news/', blank=True, null=True)
    status = models.BooleanField(default=True)

    class Meta:
        managed = False
        db_table = 'News'
    
    def __str__(self):
        return self

class Announcement(BaseModel):
    file = models.ImageField(db_column='File', upload_to='announcement/', blank=True, null=True)
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    startDate = models.DateTimeField(db_column='StartDate', blank=True, null=True)
    endDate = models.DateTimeField(db_column='EndDate', blank=True, null=True)
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'Announcement'
