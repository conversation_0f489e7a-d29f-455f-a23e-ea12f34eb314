from rest_framework import serializers
from .models import Department, ExternalData
from RTRDA.serializers import BaseModelSerializer


class DepartmentSerializer(BaseModelSerializer):
    class Meta:
        model = Department
        fields = '__all__'


class ExternalDataSerializer(serializers.ModelSerializer):
    department = DepartmentSerializer(read_only=True)
    departmentId = serializers.PrimaryKeyRelatedField(
        source='department', 
        queryset=Department.objects.all()
    )
    
    class Meta:
        model = ExternalData
        fields = '__all__'