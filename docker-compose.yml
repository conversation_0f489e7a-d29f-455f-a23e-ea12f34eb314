services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DB_SERVICE=RTRDA
      - DB_USER=sa
      - DB_PASSWORD=test12345*
      - DB_HOST=**********
      - DB_PORT=1433
      - BASE_URL=
      - MEDIA_PREFIX=files/
      - UPLOAD_DIR=/app/uploads
      - TZ=Asia/Bangkok
    volumes:
      - /home/<USER>/uploads:/app/uploads
