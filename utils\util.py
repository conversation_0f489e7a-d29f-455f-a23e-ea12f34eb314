import re
from datetime import datetime


def convert_str_to_bool(status):
    if status is not None and type(status) == str:
        status = status.lower()
        if status in ['true', '1', 'yes']:
            return True
        elif status in ['false', '0', 'no']:
            return False
    elif status is not None and type(status) == bool:
        return status
    return None


def format_date(date_str):
    """
    Formats a date string to yyyy-mm-dd format.
    If the date string is already in the correct format or is None, returns it as is.
    """
    if not date_str:
        return None

    try:
        from datetime import datetime
        # Try to parse the date string
        date_formats = [
            '%Y-%m-%d',  # Already in correct format
            '%d/%m/%Y',  # DD/MM/YYYY
            '%m/%d/%Y',  # MM/DD/YYYY
            '%d-%m-%Y',  # DD-MM-YYYY
            '%m-%d-%Y',  # MM-DD-YYYY
        ]

        for date_format in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, date_format)
                # Convert to yyyy-mm-dd
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue

        # If we get here, none of the formats matched
        return date_str
    except Exception:
        # If any error occurs, return the original string
        return date_str


def validate_email(email):
    """
    Validate an email address.
    """
    return re.match(r"[^@]+@[^@]+\.[^@]+", email) is not None


def validate_phone_number(phone_number):
    """
    Validate a phone number.
    """
    return re.match(r"^[0-9]{10}$", phone_number) is not None


def convert_str_to_date(date_str):
    """
    Convert a date string to a date object.
    Accepts both 'yyyy-mm-dd' and 'dd/mm/yyyy' formats.
    """
    if not date_str:
        return None

    try:
        if '/' in date_str:
            return datetime.strptime(date_str, '%d/%m/%Y')
        else:
            return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        return None


def convert_str_to_date_max_time(date_str):
    """
    Convert a date string to a date object with time set to 23:59:59.999999.
    Accepts both 'yyyy-mm-dd' and 'dd/mm/yyyy' formats.
    """
    if not date_str:
        return None

    try:
        if '/' in date_str:
            date_obj = datetime.strptime(date_str, '%d/%m/%Y').date()
        else:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

        # Combine the date with max time (23:59:59.999999)
        return datetime.combine(date_obj, datetime.max.time())
    except ValueError:
        return None


def convert_str_to_date_min_time(date_str):
    """
    Convert a date string to a date object with time set to 00:00:00.000000.
    Accepts both 'yyyy-mm-dd' and 'dd/mm/yyyy' formats.
    """
    if not date_str:
        return None

    try:
        if '/' in date_str:
            date_obj = datetime.strptime(date_str, '%d/%m/%Y').date()
        else:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

        # Combine the date with min time (00:00:00.000000)
        return datetime.combine(date_obj, datetime.min.time())
    except ValueError:
        return None


def convert_date_to_str_thai(date_str, with_time=False):
    """
    Convert a date string to a date object with time set to 00:00:00.000000.
    Accepts both 'yyyy-mm-dd' and 'dd/mm/yyyy' formats.
    """
    if not date_str:
        return None
    try:
        # Convert to Buddhist Era year (BE = CE + 543)
        buddhist_year = date_str.year + 543
        
        # Handle different date formats
        if with_time:
            return date_str.strftime(f'%d/%m/{buddhist_year} %H:%M:%S')
        else:
            return date_str.strftime(f'%d/%m/{buddhist_year}')
    except ValueError:
        return None
