from django.shortcuts import render
from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from drf_spectacular.utils import extend_schema
from .models import Department, ExternalData
from .serializers import DepartmentSerializer, ExternalDataSerializer
from utils.pagination import CustomPagination
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters

@extend_schema(
    tags=["Departments"]
)
class DepartmentViewSet(viewsets.ModelViewSet):
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (MultiPartParser, FormParser)
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name']
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        name = self.request.query_params.get('name')
        if name:
            queryset = queryset.filter(name__icontains=name)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    

@extend_schema(
    tags=["Departments"]
)
class ExternalDataViewSet(viewsets.ModelViewSet):
    queryset = ExternalData.objects.all()
    serializer_class = ExternalDataSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
