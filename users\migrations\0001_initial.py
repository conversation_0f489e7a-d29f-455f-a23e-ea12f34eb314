from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("auth", "__first__"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                ("last_login", models.DateTimeField(blank=True, null=True, verbose_name="last login")),
                ("is_superuser", models.BooleanField(default=False, help_text="Designates that this user has all permissions without explicitly assigning them.", verbose_name="superuser status")),
                ("username", models.Char<PERSON>ield(max_length=150, unique=True)),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("password_hash", models.Char<PERSON>ield(max_length=128)),
                ("first_name", models.Char<PERSON><PERSON>(blank=True, max_length=150, null=True)),
                ("last_name", models.Char<PERSON>ield(blank=True, max_length=150, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_admin", models.<PERSON>olean<PERSON>ield(default=False)),
                ("phone_number", models.CharField(blank=True, max_length=20, null=True)),
                ("address", models.TextField(blank=True, null=True)),
                ("bio", models.TextField(blank=True, null=True)),
                ("profile_image_url", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("groups", models.ManyToManyField(blank=True, help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.", related_name="user_set", related_query_name="user", to="auth.group", verbose_name="groups")),
                ("user_permissions", models.ManyToManyField(blank=True, help_text="Specific permissions for this user.", related_name="user_set", related_query_name="user", to="auth.permission", verbose_name="user permissions")),
            ],
            options={
                "db_table": "users",
                "managed": False,
            },
        ),
    ]
