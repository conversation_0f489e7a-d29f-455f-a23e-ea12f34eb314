from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import Research, ResearchAgency, ResearchResearchAgency, ResearchResearcher, Researcher, ResearchAgencySupportRailwayResearch
from rails.models import AgencySupportRailwayResearch
from rails.serializers import AgencySupportRailwayResearchSerializer


class ResearchSerializer(BaseModelSerializer):
    class Meta:
        model = Research
        fields = '__all__'


class ResearcherSerializer(serializers.ModelSerializer):
    class Meta:
        model = Researcher
        fields = '__all__'


class ResearchAgencySerializer(serializers.ModelSerializer):
    class Meta:
        model = ResearchAgency
        fields = '__all__'


class ResearchResearchAgencySerializer(serializers.ModelSerializer):
    research = ResearchSerializer(read_only=True)
    researchAgency = ResearchAgencySerializer(read_only=True)
    researchId = serializers.PrimaryKeyRelatedField(
        source='research', 
        queryset=Research.objects.all()
    )
    researchAgencyId = serializers.PrimaryKeyRelatedField(
        source='researchAgency', 
        queryset=ResearchAgency.objects.all()
    )
    class Meta:
        model = ResearchResearchAgency
        fields = '__all__'


class ResearchResearcherSerializer(serializers.ModelSerializer):
    research = ResearchSerializer(read_only=True)
    researcher = ResearcherSerializer(read_only=True)
    researchId = serializers.PrimaryKeyRelatedField(
        source='research', 
        queryset=Research.objects.all()
    )
    researcherId = serializers.PrimaryKeyRelatedField(
        source='researcher', 
        queryset=Researcher.objects.all()
    )
    researchAgency = ResearchAgencySerializer(read_only=True)
    researchAgencies = serializers.SerializerMethodField()
    
    class Meta:
        model = ResearchResearcher
        fields = '__all__'
        
    def get_researchAgencies(self, obj):
        # Get all research agencies for this research
        research_agency_relations = ResearchResearchAgency.objects.filter(research__id=obj.research.id)
        if research_agency_relations.exists():
            return [ResearchAgencySerializer(relation.researchAgency).data for relation in research_agency_relations]
        return []


class ResearchDetailSerializer(serializers.ModelSerializer):
    researchers = serializers.SerializerMethodField()
    agencies = serializers.SerializerMethodField()
    
    class Meta:
        model = Research
        fields = '__all__'
    
    def get_researchers(self, obj):
        researcher_ids = ResearchResearcher.objects.filter(researchId=obj.id).values_list('researcherId', flat=True)
        researchers = Researcher.objects.filter(id__in=researcher_ids)
        return ResearcherSerializer(researchers, many=True).data
    
    def get_agencies(self, obj):
        agency_ids = ResearchResearchAgency.objects.filter(researchId=obj.id).values_list('researchAgencyId', flat=True)
        agencies = ResearchAgency.objects.filter(id__in=agency_ids)
        return ResearchAgencySerializer(agencies, many=True).data 
    

class ResearchAgencySupportRailwayResearchSerializer(serializers.ModelSerializer):
    research = ResearchSerializer(read_only=True)
    agencySupportRailwayResearch = AgencySupportRailwayResearchSerializer(read_only=True)
    researchId = serializers.PrimaryKeyRelatedField(
        source='research', 
        queryset=Research.objects.all()
    )
    agencySupportRailwayResearchId = serializers.PrimaryKeyRelatedField(
        source='agencySupportRailwayResearch', 
        queryset=AgencySupportRailwayResearch.objects.all()
    )
    class Meta:
        model = ResearchAgencySupportRailwayResearch
        fields = '__all__'