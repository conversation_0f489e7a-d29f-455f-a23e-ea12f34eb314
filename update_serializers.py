#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to update serializers to use BaseModelSerializer instead of ModelSerializer
for all models that inherit from BaseModel
"""
import os
import re
import sys

def find_model_files():
    """Find all model files that import BaseModel"""
    base_model_imports = {}
    
    for root, dirs, files in os.walk('.'):
        if 'venv' in root or '.git' in root or '__pycache__' in root:
            continue
            
        for file in files:
            if file == 'models.py':
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'from RTRDA.model import BaseModel' in content:
                        # Extract model names that inherit from BaseModel
                        app_name = os.path.basename(root)
                        models = re.findall(r'class\s+(\w+)\(BaseModel\):', content)
                        if models:
                            base_model_imports[app_name] = models
    
    return base_model_imports

def update_serializer_files(base_model_imports):
    """Update serializer files to use BaseModelSerializer"""
    changes_made = 0
    
    for root, dirs, files in os.walk('.'):
        if 'venv' in root or '.git' in root or '__pycache__' in root:
            continue
            
        for file in files:
            if file == 'serializers.py':
                app_name = os.path.basename(root)
                if app_name not in base_model_imports:
                    continue
                    
                models_to_update = base_model_imports[app_name]
                file_path = os.path.join(root, file)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Add import for BaseModelSerializer if needed
                if 'from RTRDA.serializers import BaseModelSerializer' not in content:
                    content = re.sub(
                        r'from rest_framework import serializers', 
                        'from rest_framework import serializers\nfrom RTRDA.serializers import BaseModelSerializer', 
                        content
                    )
                
                # Update serializers for models that inherit from BaseModel
                for model_name in models_to_update:
                    pattern = rf'class\s+{model_name}Serializer\(serializers\.ModelSerializer\):'
                    replacement = f'class {model_name}Serializer(BaseModelSerializer):'
                    if re.search(pattern, content):
                        content = re.sub(pattern, replacement, content)
                        changes_made += 1
                        print(f"Updated {model_name}Serializer in {file_path}")
                
                # Write the changes back to the file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
    
    return changes_made

if __name__ == "__main__":
    print("Finding model files that import BaseModel...")
    base_model_imports = find_model_files()
    
    print(f"Found {sum(len(models) for models in base_model_imports.values())} models that inherit from BaseModel:")
    for app, models in base_model_imports.items():
        print(f"  {app}: {', '.join(models)}")
    
    if input("Update serializer files? (y/n): ").lower() == 'y':
        changes = update_serializer_files(base_model_imports)
        print(f"Made {changes} changes to serializer files.")
    else:
        print("No changes made.") 