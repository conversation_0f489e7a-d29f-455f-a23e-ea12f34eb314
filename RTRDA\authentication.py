from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.exceptions import InvalidToken, AuthenticationFailed
from rest_framework.authentication import get_authorization_header
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from datetime import datetime
from django.conf import settings


class CustomJWTAuthentication(JWTAuthentication):
    """
    Custom JWT Authentication class that extends the default JWT authentication
    with additional validation and error handling.
    """
    
    def authenticate(self, request):
        """
        Authenticate the request and return a tuple of (user, token).
        """
        header = get_authorization_header(request)
        
        if not header:
            return None
            
        try:
            # Get the raw token
            raw_token = self.get_raw_token(header)
            if raw_token is None:
                return None
                
            # Validate the token
            validated_token = self.get_validated_token(raw_token)
            
            # Get the user from the validated token
            user = self.get_user(validated_token)
            
            # Additional custom validations
            self.validate_user(user)
            self.validate_token_expiry(validated_token)
            
            return user, validated_token
            
        except InvalidToken as e:
            raise AuthenticationFailed({
                'status': 'error',
                'code': 'token_invalid',
                'detail': str(e),
            }, code=status.HTTP_401_UNAUTHORIZED)
            
        except Exception as e:
            raise AuthenticationFailed({
                'status': 'error',
                'code': 'authentication_failed',
                'detail': str(e),
            }, code=status.HTTP_401_UNAUTHORIZED)
    
    def validate_user(self, user):
        """
        Additional user validation checks.
        """
        if not user.is_active:
            raise AuthenticationFailed({
                'status': 'error',
                'code': 'user_inactive',
                'detail': _('User account is disabled.'),
            })
            
        # Check if user is locked out
        if user.lockoutEnabled and user.lockoutEndDateUtc and user.lockoutEndDateUtc > datetime.now():
            raise AuthenticationFailed({
                'status': 'error',
                'code': 'account_locked',
                'detail': _('Your account is temporarily locked. Please try again later.'),
            })
    
    def validate_token_expiry(self, token):
        """
        Additional token expiry validation.
        """
        exp_timestamp = token.get('exp')
        if not exp_timestamp:
            raise InvalidToken({
                'status': 'error',
                'code': 'token_invalid',
                'detail': _('Token has no expiration'),
            })
            
        exp_datetime = datetime.fromtimestamp(exp_timestamp)
        if exp_datetime < datetime.now():
            raise InvalidToken({
                'status': 'error',
                'code': 'token_expired',
                'detail': _('Token has expired'),
            })
    
    def get_raw_token(self, header):
        """
        Extract the raw token from the authorization header.
        """
        parts = header.decode().split()
        
        if len(parts) == 0:
            return None
            
        if parts[0] not in settings.SIMPLE_JWT['AUTH_HEADER_TYPES']:
            return None
            
        if len(parts) != 2:
            raise InvalidToken({
                'status': 'error',
                'code': 'invalid_header',
                'detail': _('Invalid authorization header format. Expected format: "Bearer token"'),
            })
            
        return parts[1]
