from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import Testing, TestingCenterAgency, TestingCenter, Service, StandardTestingCenter
from mas.serializers import StandardSerializer, MasGeographySerializer, MasProvinceSerializer, MasDistrictSerializer, MasSubdistrictSerializer
from mas.models import Standard, MasGeography, MasProvince, MasDistrict, MasSubdistrict

class TestingSerializer(BaseModelSerializer):
    standard = StandardSerializer(read_only=True)
    standardId = serializers.PrimaryKeyRelatedField(
        source='standard',
        queryset=Standard.objects.all()
    )
    class Meta:
        model = Testing
        fields = '__all__'


class TestingCenterAgencySerializer(BaseModelSerializer):
    masGeography = MasGeographySerializer(read_only=True)
    masProvince = MasProvinceSerializer(read_only=True)
    masDistrict = MasDistrictSerializer(read_only=True)
    masSubdistrict = MasSubdistrictSerializer(read_only=True)
    masGeographyId = serializers.PrimaryKeyRelatedField(
        source='masGeography', 
        queryset=MasGeography.objects.all()
    )
    masProvinceId = serializers.PrimaryKeyRelatedField(
        source='masProvince', 
        queryset=MasProvince.objects.all()
    )
    masDistrictId = serializers.PrimaryKeyRelatedField(
        source='masDistrict', 
        queryset=MasDistrict.objects.all()
    )
    masSubdistrictId = serializers.PrimaryKeyRelatedField(
        source='masSubdistrict', 
        queryset=MasSubdistrict.objects.all()
    )
    class Meta:
        model = TestingCenterAgency
        fields = '__all__'


class TestingCenterSerializer(BaseModelSerializer):
    testingCenterAgency = TestingCenterAgencySerializer(read_only=True)
    testingCenterAgencyId = serializers.PrimaryKeyRelatedField(
        source='testingCenterAgency',
        queryset=TestingCenterAgency.objects.all()
    )

    class Meta:
        model = TestingCenter
        fields = '__all__'


class ServiceSerializer(BaseModelSerializer):
    testingCenter = TestingCenterSerializer(read_only=True)
    testingCenterId = serializers.PrimaryKeyRelatedField(
        source='testingCenter',
        queryset=TestingCenter.objects.all()
    )

    class Meta:
        model = Service
        fields = '__all__'


class StandardTestingCenterSerializer(serializers.ModelSerializer):
    standard = StandardSerializer(read_only=True)
    testingCenter = TestingCenterSerializer(read_only=True)
    standardId = serializers.PrimaryKeyRelatedField(
        source='standard',
        queryset=Standard.objects.all()
    )
    testingCenterId = serializers.PrimaryKeyRelatedField(
        source='testingCenter',
        queryset=TestingCenter.objects.all()
    )

    class Meta:
        model = StandardTestingCenter
        fields = '__all__'
